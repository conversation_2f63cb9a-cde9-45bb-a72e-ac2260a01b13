import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Alert } from 'antd';
import { useSecureAuth } from '../contexts/SecureAuthContext';
import secureApiService from '../services/secureApiService';

const { Title, Text, Paragraph } = Typography;

const AuthDebugger: React.FC = () => {
  const { isAuthenticated, isAdmin, isMainAdmin, admin, loading, error } = useSecureAuth();
  const [testResults, setTestResults] = useState<any[]>([]);

  const addTestResult = (test: string, result: any, success: boolean) => {
    setTestResults(prev => [...prev, { test, result, success, timestamp: new Date().toISOString() }]);
  };

  const testHealthCheck = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/health');
      const data = await response.json();
      addTestResult('Health Check', data, true);
    } catch (error) {
      addTestResult('Health Check', error, false);
    }
  };

  const testAdminLogin = async () => {
    try {
      const response = await secureApiService.adminLogin('<EMAIL>', 'admin123');
      addTestResult('Admin Login', response, true);
    } catch (error) {
      addTestResult('Admin Login', error, false);
    }
  };

  const testFetchAdminLogin = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        })
      });
      const data = await response.json();
      addTestResult('Fetch Admin Login', data, response.ok);
    } catch (error) {
      addTestResult('Fetch Admin Login', error, false);
    }
  };

  const testAdminProfile = async () => {
    try {
      const response = await secureApiService.getAdminProfile();
      addTestResult('Admin Profile', response, true);
    } catch (error) {
      addTestResult('Admin Profile', error, false);
    }
  };

  const testScholarships = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/scholarships');
      const data = await response.json();
      addTestResult('Scholarships', data, true);
    } catch (error) {
      addTestResult('Scholarships', error, false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Authentication Debugger</Title>
      
      <Card title="Current Auth State" style={{ marginBottom: '20px' }}>
        <Space direction="vertical">
          <Text><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</Text>
          <Text><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</Text>
          <Text><strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}</Text>
          <Text><strong>Is Main Admin:</strong> {isMainAdmin ? 'Yes' : 'No'}</Text>
          <Text><strong>Admin Data:</strong> {admin ? JSON.stringify(admin, null, 2) : 'None'}</Text>
          {error && <Alert message={error} type="error" />}
        </Space>
      </Card>

      <Card title="API Tests" style={{ marginBottom: '20px' }}>
        <Space wrap>
          <Button onClick={testHealthCheck}>Test Health Check</Button>
          <Button onClick={testFetchAdminLogin} type="primary">Test Fetch Admin Login</Button>
          <Button onClick={testAdminLogin}>Test Axios Admin Login</Button>
          <Button onClick={testAdminProfile}>Test Admin Profile</Button>
          <Button onClick={testScholarships}>Test Scholarships</Button>
          <Button onClick={clearResults} danger>Clear Results</Button>
        </Space>
      </Card>

      <Card title="Test Results">
        {testResults.length === 0 ? (
          <Text>No tests run yet</Text>
        ) : (
          <Space direction="vertical" style={{ width: '100%' }}>
            {testResults.map((result, index) => (
              <Alert
                key={index}
                message={`${result.test} - ${result.success ? 'SUCCESS' : 'FAILED'}`}
                description={
                  <div>
                    <Text code>{result.timestamp}</Text>
                    <Paragraph>
                      <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                        {JSON.stringify(result.result, null, 2)}
                      </pre>
                    </Paragraph>
                  </div>
                }
                type={result.success ? 'success' : 'error'}
                style={{ marginBottom: '10px' }}
              />
            ))}
          </Space>
        )}
      </Card>
    </div>
  );
};

export default AuthDebugger;
