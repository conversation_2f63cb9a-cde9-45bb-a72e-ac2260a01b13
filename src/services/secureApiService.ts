import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { getBoolEnv } from '../utils/envValidator';

/**
 * Secure API service using HTTP-only cookies for authentication
 * This is a more secure approach than using localStorage for tokens
 */
class SecureApiService {
  private api: AxiosInstance;
  private csrfToken: string | null = null;

  constructor() {
    // Create axios instance with default configuration
    const baseURL = 'http://localhost:5000'; // Hardcode the URL for testing
    console.log('Initializing secure API service with base URL:', baseURL);

    this.api = axios.create({
      baseURL: `${baseURL}/api`,
      timeout: 15000, // 15 seconds
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true, // Important for HTTP-only cookies
    });

    // Test the API connection
    this.testApiConnection();

    // Set up interceptors
    this.setupInterceptors();

    console.log('Secure API service initialized with withCredentials:', true);
  }

  /**
   * Test the API connection
   * This is a helper method to diagnose connection issues
   */
  private async testApiConnection() {
    try {
      console.log('Testing API connection...');
      const response = await fetch('http://localhost:5000/api/health');
      const data = await response.json();
      console.log('API connection test successful:', data);
    } catch (error) {
      console.error('API connection test failed:', error);
    }
  }

  /**
   * Set up request and response interceptors
   */
  private setupInterceptors() {
    // Request interceptor for adding CSRF token
    this.api.interceptors.request.use(
      (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
        // Add CSRF token if available
        if (this.csrfToken) {
          config.headers = config.headers || {};
          config.headers['X-CSRF-Token'] = this.csrfToken;
        }

        // Add additional headers if needed
        if (getBoolEnv('REACT_APP_DEBUG_MODE', false)) {
          config.headers = config.headers || {};
          config.headers['X-Debug-Mode'] = 'true';
        }

        return config;
      },
      (error: AxiosError): Promise<AxiosError> => {
        // Log request errors in development
        if (process.env.NODE_ENV === 'development') {
          console.error('Request error:', error);
        }
        return Promise.reject(error);
      }
    );

    // Response interceptor for handling common response patterns and errors
    this.api.interceptors.response.use(
      (response: AxiosResponse): AxiosResponse => {
        // Store CSRF token if present in response
        if (response.data && response.data.csrfToken) {
          this.csrfToken = response.data.csrfToken;
        }
        return response;
      },
      (error: AxiosError): Promise<AxiosError> => {
        // Handle 401 Unauthorized errors (token expired or invalid)
        if (error.response && error.response.status === 401) {
          // Redirect to login page if not already there
          if (!window.location.pathname.includes('/login') && !window.location.pathname.includes('/secure-login')) {
            console.log('Session expired. Redirecting to login page...');
            window.location.href = '/admin/secure-login';
          }
        }

        // Log all errors in development
        if (process.env.NODE_ENV === 'development') {
          console.error('Response error:', error);

          if (error.response) {
            console.error('Error data:', error.response.data);
            console.error('Error status:', error.response.status);
          } else if (error.request) {
            console.error('No response received:', error.request);
          } else {
            console.error('Error setting up request:', error.message);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Set CSRF token manually
   * @param token CSRF token
   */
  setCsrfToken(token: string): void {
    this.csrfToken = token;
  }

  /**
   * Get CSRF token
   * @returns CSRF token
   */
  getCsrfToken(): string | null {
    return this.csrfToken;
  }

  /**
   * Make a GET request
   * @param url - The URL to request
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.get<T>(url, config);
    return response.data;
  }

  /**
   * Make a POST request
   * @param url - The URL to request
   * @param data - The data to send
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.post<T>(url, data, config);
    return response.data;
  }

  /**
   * Make a PUT request
   * @param url - The URL to request
   * @param data - The data to send
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.put<T>(url, data, config);
    return response.data;
  }

  /**
   * Make a DELETE request
   * @param url - The URL to request
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.delete<T>(url, config);
    return response.data;
  }

  /**
   * Upload a file with multipart/form-data
   * @param url - The URL to request
   * @param formData - The FormData object with file and other data
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  async upload<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.post<T>(url, formData, {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Login a user
   * @param email User email
   * @param password User password
   * @returns Promise with the response data
   */
  async login(email: string, password: string): Promise<any> {
    return this.post('/auth/login', { email, password });
  }

  /**
   * Login an admin
   * @param email Admin email
   * @param password Admin password
   * @returns Promise with the response data
   */
  async adminLogin(email: string, password: string): Promise<any> {
    console.log('Attempting admin login with:', email);
    try {
      // Add more detailed logging
      console.log('Making request to /admin/login with credentials');

      // Make the request with explicit configuration
      const response = await this.api.post('/admin/login', { email, password }, {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('Admin login response:', response);

      // Force a reload of the page to ensure the cookie is properly set
      if (response.data.success) {
        console.log('Login successful, will reload page to ensure cookie is set');
        setTimeout(() => {
          window.location.href = '/admin/dashboard';
        }, 1000);
      }

      const result = response.data;
      console.log('Admin login successful:', result);
      return result;
    } catch (error) {
      console.error('Admin login failed:', error);
      throw error;
    }
  }

  /**
   * Logout a user or admin
   * @returns Promise with the response data
   */
  async logout(): Promise<any> {
    try {
      // Try admin logout first
      console.log('Attempting admin logout...');
      const response = await this.post('/admin/logout');
      console.log('Admin logout response:', response);
      return response;
    } catch (error) {
      console.log('Admin logout failed, trying user logout...');
      // If admin logout fails, try user logout
      return this.post('/auth/logout');
    }
  }

  /**
   * Get current user profile
   * @returns Promise with the response data
   */
  async getProfile(): Promise<any> {
    return this.get('/auth/profile');
  }

  /**
   * Get current admin profile
   * @returns Promise with the response data
   */
  async getAdminProfile(): Promise<any> {
    console.log('Fetching admin profile');
    try {
      // Add more detailed logging
      console.log('Making request to /admin/current with cookies:', document.cookie);

      // Make the request with explicit configuration
      const response = await this.api.get('/admin/current', {
        withCredentials: true
      });

      console.log('Admin profile response:', response);

      const result = response.data;
      console.log('Admin profile fetch successful:', result);
      return result;
    } catch (error: any) {
      console.error('Admin profile fetch failed:', error);
      console.error('Error details:', error.response?.data || error.message || 'Unknown error');
      throw error;
    }
  }
}

// Create a singleton instance
const secureApiService = new SecureApiService();

export default secureApiService;
