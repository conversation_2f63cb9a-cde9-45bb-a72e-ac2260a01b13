import axios from 'axios';
import { Scholarship } from '../components/ScholarshipGrid';

// Base API URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

/**
 * Service for fetching and filtering scholarships
 */
const scholarshipService = {
  /**
   * Get all scholarships
   */
  getAllScholarships: async (): Promise<Scholarship[]> => {
    try {
      console.log('Fetching scholarships from API...');
      const response = await axios.get(`${API_URL}/api/scholarships`);
      console.log('API response:', response.data);

      // Handle the correct API response format: { success: true, data: [...] }
      const scholarshipsData = response.data.data || response.data.scholarships || [];

      return scholarshipsData.map((scholarship: any) => ({
        id: scholarship.id,
        title: scholarship.title,
        thumbnail: scholarship.thumbnail,
        deadline: scholarship.deadline,
        isOpen: scholarship.isOpen,
        level: scholarship.level,
        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
        country: scholarship.country || 'France',
      }));
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      return getFallbackScholarships();
    }
  },

  /**
   * Get scholarships by level (Licence, Master, Doctorat)
   */
  getScholarshipsByLevel: async (level: string): Promise<Scholarship[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/scholarships?level=${level}`);
      return response.data.scholarships.map((scholarship: any) => ({
        id: scholarship.id,
        title: scholarship.title,
        thumbnail: scholarship.thumbnail,
        deadline: scholarship.deadline,
        isOpen: scholarship.isOpen,
        level: scholarship.level,
        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
        country: scholarship.country || 'France',
      }));
    } catch (error) {
      console.error(`Error fetching scholarships by level ${level}:`, error);
      // Filter fallback data by level
      return getFallbackScholarships().filter(
        (s) => s.level?.toLowerCase() === level.toLowerCase()
      );
    }
  },

  /**
   * Get scholarships by funding source
   */
  getScholarshipsBySource: async (source: string): Promise<Scholarship[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/scholarships?fundingSource=${source}`);
      return response.data.scholarships.map((scholarship: any) => ({
        id: scholarship.id,
        title: scholarship.title,
        thumbnail: scholarship.thumbnail,
        deadline: scholarship.deadline,
        isOpen: scholarship.isOpen,
        level: scholarship.level,
        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
        country: scholarship.country || 'France',
      }));
    } catch (error) {
      console.error(`Error fetching scholarships by source ${source}:`, error);
      // Filter fallback data by source
      return getFallbackScholarships().filter(
        (s) => s.fundingSource?.toLowerCase().includes(source.toLowerCase())
      );
    }
  },

  /**
   * Get latest scholarships
   */
  getLatestScholarships: async (limit: number = 6): Promise<Scholarship[]> => {
    try {
      console.log('Fetching latest scholarships from API...');
      const response = await axios.get(`${API_URL}/api/scholarships?sortBy=createdAt&sortOrder=desc&limit=${limit}`);
      console.log('Latest scholarships API response:', response.data);

      // Handle the correct API response format: { success: true, data: [...] }
      const scholarshipsData = response.data.data || response.data.scholarships || [];

      return scholarshipsData.map((scholarship: any) => ({
        id: scholarship.id,
        title: scholarship.title,
        thumbnail: scholarship.thumbnail,
        deadline: scholarship.deadline,
        isOpen: scholarship.isOpen,
        level: scholarship.level,
        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
        country: scholarship.country || 'France',
      }));
    } catch (error) {
      console.error('Error fetching latest scholarships:', error);
      // Return first 'limit' scholarships from fallback data
      return getFallbackScholarships().slice(0, limit);
    }
  },
};

/**
 * Helper function to determine funding source from scholarship data
 */
function determineSourceFromData(scholarship: any): string {
  if (scholarship.title?.toLowerCase().includes('gouvernement') ||
      scholarship.description?.toLowerCase().includes('gouvernement')) {
    return 'Gouvernement';
  } else if (scholarship.title?.toLowerCase().includes('université') ||
             scholarship.description?.toLowerCase().includes('université')) {
    return 'Université';
  } else {
    return 'Organisation';
  }
}

/**
 * Fallback scholarships data when API is unavailable
 */
function getFallbackScholarships(): Scholarship[] {
  return [
    {
      id: 1,
      title: 'Bourse d\'Excellence en Informatique',
      thumbnail: '/assets/scholarship1.jpg',
      deadline: '2024-06-30',
      isOpen: true,
      level: 'Licence',
      fundingSource: 'Université',
      country: 'France',
    },
    {
      id: 2,
      title: 'Programme de Bourse en Génie Civil',
      thumbnail: '/assets/scholarship2.jpg',
      deadline: '2024-05-15',
      isOpen: true,
      level: 'Master',
      fundingSource: 'Gouvernement',
      country: 'Canada',
    },
    {
      id: 3,
      title: 'Bourse Internationale en Médecine',
      thumbnail: '/assets/scholarship3.jpg',
      deadline: '2024-04-30',
      isOpen: false,
      level: 'Doctorat',
      fundingSource: 'Organisation',
      country: 'Belgique',
    },
    // Add more fallback scholarships as needed
  ];
}

export default scholarshipService;
