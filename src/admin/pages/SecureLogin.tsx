import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, Alert, Typography, Divider } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { ApiResponse } from '../../types/api';
import { useSecureAuth } from '../../contexts/SecureAuthContext';
import secureApiService from '../../services/secureApiService';
import AuthFeedback, { AuthStatus } from '../../components/AuthFeedback';
import AuthDebugger from '../../components/AuthDebugger';

const { Title, Text } = Typography;

const SecureLogin: React.FC = () => {
  const navigate = useNavigate();
  const { adminLogin, isAuthenticated, isAdmin, loading, error, clearError } = useSecureAuth();
  const [form] = Form.useForm();
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [twoFactorToken, setTwoFactorToken] = useState('');
  const [adminId, setAdminId] = useState<number | null>(null);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [authStatus, setAuthStatus] = useState<AuthStatus>(AuthStatus.IDLE);

  // Redirect if already authenticated
  useEffect(() => {
    console.log('Authentication state changed:', { isAuthenticated, isAdmin, loading });
    if (isAuthenticated && isAdmin && !loading) {
      console.log('Redirecting to dashboard...');
      // Use React Router navigation instead of hard redirect
      navigate('/admin/dashboard');
    }
  }, [isAuthenticated, isAdmin, loading, navigate]);

  // Clear errors when component unmounts
  useEffect(() => {
    return () => {
      clearError();
      setLoginError(null);
    };
  }, [clearError]);

  // Handle form submission
  const onFinish = async (values: { email: string; password: string }) => {
    try {
      setLoginError(null);
      setAuthStatus(AuthStatus.LOADING);
      console.log('Attempting to login with:', values.email);

      // Test direct API call first
      try {
        console.log('Testing direct API call to backend...');
        const response = await fetch('http://localhost:5000/api/health');
        const data = await response.json();
        console.log('API health check successful:', data);
      } catch (error) {
        console.error('API health check failed:', error);
      }

      // Attempt to login
      await adminLogin(values.email, values.password);

      // If we get here, login was successful
      console.log('Login successful, setting success state');
      setAuthStatus(AuthStatus.SUCCESS);

      // Navigate without page reload - the useEffect will handle the redirect
      console.log('Login successful, waiting for auth state update...');
    } catch (err: any) {
      console.error('Login error:', err);
      // Check if 2FA is required
      if (err.response?.status === 403 && err.response?.data?.requireTwoFactor) {
        setShowTwoFactor(true);
        setAdminId(err.response.data.adminId);
        setAuthStatus(AuthStatus.INFO);
      } else {
        const errorMessage = err.response?.data?.message || err.message || 'Login failed';
        console.error('Setting error state:', errorMessage);
        setLoginError(errorMessage);
        setAuthStatus(AuthStatus.ERROR);
      }
    }
  };

  // Handle 2FA verification
  const handleTwoFactorVerify = async () => {
    try {
      if (!adminId) {
        setLoginError('Admin ID is missing');
        setAuthStatus(AuthStatus.ERROR);
        return;
      }

      setAuthStatus(AuthStatus.LOADING);

      const response = await secureApiService.post<ApiResponse>('/2fa/verify', {
        adminId,
        token: twoFactorToken
      });

      if (response.success) {
        setAuthStatus(AuthStatus.SUCCESS);

        // Delay navigation to show success message
        setTimeout(() => {
          // Use React Router navigation instead of hard redirect
          navigate('/admin/dashboard');
        }, 2000);
      } else {
        setLoginError(response.message || 'Two-factor authentication failed');
        setAuthStatus(AuthStatus.ERROR);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Two-factor authentication failed';
      setLoginError(errorMessage);
      setAuthStatus(AuthStatus.ERROR);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md shadow-lg">
        <div className="text-center mb-6">
          <Title level={2} className="text-primary">
            Admin Portal
          </Title>
          <Text type="secondary">
            Secure login with HTTP-only cookies
          </Text>
        </div>

        {/* Authentication feedback */}
        {authStatus === AuthStatus.LOADING && (
          <AuthFeedback
            status={AuthStatus.LOADING}
            title="Authenticating"
            message="Please wait while we verify your credentials..."
            showProgress={false}
          />
        )}

        {authStatus === AuthStatus.SUCCESS && (
          <AuthFeedback
            status={AuthStatus.SUCCESS}
            title="Login Successful"
            message="You have been successfully authenticated."
            details="Redirecting to dashboard..."
            autoHideDuration={2000}
          />
        )}

        {authStatus === AuthStatus.ERROR && (
          <AuthFeedback
            status={AuthStatus.ERROR}
            title="Login Error"
            message={loginError || error || "Authentication failed"}
            details="Please check your credentials and try again."
            onClose={() => {
              clearError();
              setLoginError(null);
              setAuthStatus(AuthStatus.IDLE);
            }}
          />
        )}

        {authStatus === AuthStatus.INFO && showTwoFactor && (
          <AuthFeedback
            status={AuthStatus.INFO}
            title="Two-Factor Authentication Required"
            message="Please enter the verification code from your authenticator app."
            showProgress={false}
          />
        )}

        {!showTwoFactor ? (
          <Form
            form={form}
            name="secure_login"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            layout="vertical"
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: 'Please input your email!' },
                { type: 'email', message: 'Please enter a valid email address!' }
              ]}
            >
              <Input
                prefix={<UserOutlined className="site-form-item-icon" />}
                placeholder="Email"
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: 'Please input your password!' }]}
            >
              <Input.Password
                prefix={<LockOutlined className="site-form-item-icon" />}
                placeholder="Password"
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className="w-full"
                size="large"
                loading={loading}
              >
                Log in
              </Button>
            </Form.Item>

            <div className="text-center">
              <Link to="/admin/forgot-password" className="text-primary hover:underline">
                Forgot password?
              </Link>
            </div>
          </Form>
        ) : (
          <div>
            <Alert
              message="Two-Factor Authentication Required"
              description="Please enter the verification code from your authenticator app."
              type="info"
              showIcon
              className="mb-4"
            />

            <div className="mb-4">
              <Input
                prefix={<SafetyOutlined className="site-form-item-icon" />}
                placeholder="Verification Code"
                size="large"
                value={twoFactorToken}
                onChange={(e) => setTwoFactorToken(e.target.value)}
                maxLength={6}
              />
            </div>

            <Button
              type="primary"
              className="w-full"
              size="large"
              onClick={handleTwoFactorVerify}
              loading={loading}
            >
              Verify
            </Button>

            <Button
              type="link"
              className="w-full mt-2"
              onClick={() => {
                setShowTwoFactor(false);
                setTwoFactorToken('');
                setAdminId(null);
              }}
            >
              Back to Login
            </Button>
          </div>
        )}

        <Divider />

        <div className="text-center text-sm text-gray-500">
          <p>This is a secure login that uses HTTP-only cookies for authentication.</p>
          <p>Your session will be protected against CSRF attacks.</p>
        </div>
      </Card>

      {/* Debug panel - only show in development */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ marginTop: '20px' }}>
          <AuthDebugger />
        </div>
      )}
    </div>
  );
};

export default SecureLogin;
