[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AuthContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AdminContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdminProtectedRoute.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLoginTester.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipsManager.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/MessagesManager.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/clearStorage.js": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/testUtils.js": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/apiConfig.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorVerification.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/BulkScholarshipImport.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "152", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "153", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "154", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "155", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx": "156", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/secureApiService.ts": "157", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx": "158", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "159", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SecureAdminProtectedRoute.tsx": "160", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "161", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthFeedback.tsx": "162", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx": "163", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/authMigration.ts": "164"}, {"size": 274, "mtime": *************, "results": "165", "hashOfConfig": "166"}, {"size": 2118, "mtime": *************, "results": "167", "hashOfConfig": "166"}, {"size": 5438, "mtime": *************, "results": "168", "hashOfConfig": "166"}, {"size": 10242, "mtime": *************, "results": "169", "hashOfConfig": "166"}, {"size": 1742, "mtime": *************, "results": "170", "hashOfConfig": "166"}, {"size": 7131, "mtime": *************, "results": "171", "hashOfConfig": "166"}, {"size": 10498, "mtime": 1745982258490, "results": "172", "hashOfConfig": "166"}, {"size": 3076, "mtime": 1745945973317, "results": "173", "hashOfConfig": "166"}, {"size": 553, "mtime": 1745978322072, "results": "174", "hashOfConfig": "166"}, {"size": 6250, "mtime": 1745977773426, "results": "175", "hashOfConfig": "166"}, {"size": 2446, "mtime": 1745945132621, "results": "176", "hashOfConfig": "166"}, {"size": 2645, "mtime": 1745977581979, "results": "177", "hashOfConfig": "166"}, {"size": 4167, "mtime": 1745981843303, "results": "178", "hashOfConfig": "166"}, {"size": 5713, "mtime": 1745981758114, "results": "179", "hashOfConfig": "166"}, {"size": 675, "mtime": 1745976791748, "results": "180", "hashOfConfig": "166"}, {"size": 1059, "mtime": 1745976720607, "results": "181", "hashOfConfig": "166"}, {"size": 3452, "mtime": 1745946003719, "results": "182", "hashOfConfig": "166"}, {"size": 2518, "mtime": 1745983866923, "results": "183", "hashOfConfig": "166"}, {"size": 1737, "mtime": 1745978376608, "results": "184", "hashOfConfig": "166"}, {"size": 4075, "mtime": 1745982269507, "results": "185", "hashOfConfig": "166"}, {"size": 4531, "mtime": 1745982263875, "results": "186", "hashOfConfig": "166"}, {"size": 5406, "mtime": 1745982274929, "results": "187", "hashOfConfig": "166"}, {"size": 2535, "mtime": 1745978386143, "results": "188", "hashOfConfig": "166"}, {"size": 737, "mtime": 1745944438688, "results": "189", "hashOfConfig": "166"}, {"size": 2323, "mtime": 1745982233889, "results": "190", "hashOfConfig": "166"}, {"size": 274, "mtime": *************, "results": "191", "hashOfConfig": "192"}, {"size": 4324, "mtime": 1746276088446, "results": "193", "hashOfConfig": "192"}, {"size": 7131, "mtime": *************, "results": "194", "hashOfConfig": "192"}, {"size": 10498, "mtime": 1745982258490, "results": "195", "hashOfConfig": "192"}, {"size": 10242, "mtime": *************, "results": "196", "hashOfConfig": "192"}, {"size": 1742, "mtime": *************, "results": "197", "hashOfConfig": "192"}, {"size": 8315, "mtime": 1746204095547, "results": "198", "hashOfConfig": "192"}, {"size": 3076, "mtime": 1745945973317, "results": "199", "hashOfConfig": "192"}, {"size": 2535, "mtime": 1746033992389, "results": "200", "hashOfConfig": "192"}, {"size": 1737, "mtime": 1745978376608, "results": "201", "hashOfConfig": "192"}, {"size": 6250, "mtime": 1745977773426, "results": "202", "hashOfConfig": "192"}, {"size": 553, "mtime": 1745978322072, "results": "203", "hashOfConfig": "192"}, {"size": 2446, "mtime": 1745945132621, "results": "204", "hashOfConfig": "192"}, {"size": 5154, "mtime": 1746276100698, "results": "205", "hashOfConfig": "192"}, {"size": 8097, "mtime": 1745983506688, "results": "206", "hashOfConfig": "192"}, {"size": 6024, "mtime": 1746026061709, "results": "207", "hashOfConfig": "192"}, {"size": 9559, "mtime": 1746270350218, "results": "208", "hashOfConfig": "192"}, {"size": 737, "mtime": 1745944438688, "results": "209", "hashOfConfig": "192"}, {"size": 2645, "mtime": 1745977581979, "results": "210", "hashOfConfig": "192"}, {"size": 4531, "mtime": 1745982263875, "results": "211", "hashOfConfig": "192"}, {"size": 4075, "mtime": 1745982269507, "results": "212", "hashOfConfig": "192"}, {"size": 5406, "mtime": 1745982274929, "results": "213", "hashOfConfig": "192"}, {"size": 5713, "mtime": 1745981758114, "results": "214", "hashOfConfig": "192"}, {"size": 675, "mtime": 1745976791748, "results": "215", "hashOfConfig": "192"}, {"size": 4167, "mtime": 1745981843303, "results": "216", "hashOfConfig": "192"}, {"size": 2115, "mtime": 1746029576846, "results": "217", "hashOfConfig": "192"}, {"size": 1689, "mtime": 1745982730905, "results": "218", "hashOfConfig": "192"}, {"size": 19288, "mtime": 1745983400384, "results": "219", "hashOfConfig": "192"}, {"size": 1059, "mtime": 1745976720607, "results": "220", "hashOfConfig": "192"}, {"size": 2535, "mtime": 1745978386143, "results": "221", "hashOfConfig": "192"}, {"size": 14052, "mtime": 1746274142489, "results": "222", "hashOfConfig": "192"}, {"size": 3939, "mtime": 1746017528736, "results": "223", "hashOfConfig": "192"}, {"size": 13857, "mtime": 1746282401482, "results": "224", "hashOfConfig": "192"}, {"size": 11940, "mtime": 1746252382064, "results": "225", "hashOfConfig": "192"}, {"size": 38877, "mtime": 1746252407519, "results": "226", "hashOfConfig": "192"}, {"size": 8393, "mtime": 1746249939564, "results": "227", "hashOfConfig": "192"}, {"size": 11544, "mtime": 1746272209267, "results": "228", "hashOfConfig": "192"}, {"size": 1343, "mtime": 1746033436995, "results": "229", "hashOfConfig": "192"}, {"size": 1752, "mtime": 1746274101102, "results": "230", "hashOfConfig": "192"}, {"size": 3341, "mtime": 1746199132190, "results": "231", "hashOfConfig": "192"}, {"size": 7045, "mtime": 1746199160974, "results": "232", "hashOfConfig": "192"}, {"size": 4103, "mtime": 1746200520123, "results": "233", "hashOfConfig": "192"}, {"size": 6276, "mtime": 1746249196201, "results": "234", "hashOfConfig": "192"}, {"size": 7222, "mtime": 1746249216418, "results": "235", "hashOfConfig": "192"}, {"size": 1105, "mtime": 1746201832350, "results": "236", "hashOfConfig": "192"}, {"size": 921, "mtime": 1746202207790, "results": "237", "hashOfConfig": "192"}, {"size": 11379, "mtime": 1746276323665, "results": "238", "hashOfConfig": "192"}, {"size": 1059, "mtime": 1746226321253, "results": "239", "hashOfConfig": "192"}, {"size": 7914, "mtime": 1746251582912, "results": "240", "hashOfConfig": "192"}, {"size": 4784, "mtime": 1746252717773, "results": "241", "hashOfConfig": "192"}, {"size": 1777, "mtime": 1746254015165, "results": "242", "hashOfConfig": "192"}, {"size": 9435, "mtime": 1746252638103, "results": "243", "hashOfConfig": "192"}, {"size": 5504, "mtime": 1746275135511, "results": "244", "hashOfConfig": "192"}, {"size": 620, "mtime": 1747275499301, "results": "245", "hashOfConfig": "246"}, {"size": 5228, "mtime": 1747278552152, "results": "247", "hashOfConfig": "246"}, {"size": 13828, "mtime": 1752248890943, "results": "248", "hashOfConfig": "246"}, {"size": 19195, "mtime": 1747280789154, "results": "249", "hashOfConfig": "246"}, {"size": 3587, "mtime": 1747235972243, "results": "250", "hashOfConfig": "246"}, {"size": 10653, "mtime": 1747235897205, "results": "251", "hashOfConfig": "246"}, {"size": 2535, "mtime": 1746033992389, "results": "252", "hashOfConfig": "246"}, {"size": 8578, "mtime": 1746253995466, "results": "253", "hashOfConfig": "246"}, {"size": 3076, "mtime": 1745945973317, "results": "254", "hashOfConfig": "246"}, {"size": 1974, "mtime": 1747278879917, "results": "255", "hashOfConfig": "246"}, {"size": 1752, "mtime": 1746274101102, "results": "256", "hashOfConfig": "257"}, {"size": 553, "mtime": 1745978322072, "results": "258", "hashOfConfig": "246"}, {"size": 18635, "mtime": 1747235596822, "results": "259", "hashOfConfig": "257"}, {"size": 2446, "mtime": 1745945132621, "results": "260", "hashOfConfig": "246"}, {"size": 4784, "mtime": 1746252717773, "results": "261", "hashOfConfig": "246"}, {"size": 19928, "mtime": 1747184532558, "results": "262", "hashOfConfig": "246"}, {"size": 5619, "mtime": 1747278582985, "results": "263", "hashOfConfig": "246"}, {"size": 5504, "mtime": 1746275135511, "results": "264", "hashOfConfig": "246"}, {"size": 11544, "mtime": 1746272209267, "results": "265", "hashOfConfig": "246"}, {"size": 14051, "mtime": 1747277440650, "results": "266", "hashOfConfig": "246"}, {"size": 14143, "mtime": 1747150652537, "results": "267", "hashOfConfig": "246"}, {"size": 7045, "mtime": 1746199160974, "results": "268", "hashOfConfig": "246"}, {"size": 48926, "mtime": 1747181396095, "results": "269", "hashOfConfig": "246"}, {"size": 3341, "mtime": 1746199132190, "results": "270", "hashOfConfig": "246"}, {"size": 25795, "mtime": 1747183272913, "results": "271", "hashOfConfig": "246"}, {"size": 1059, "mtime": 1746226321253, "results": "272", "hashOfConfig": "246"}, {"size": 6276, "mtime": 1746249196201, "results": "273", "hashOfConfig": "246"}, {"size": 921, "mtime": 1746202207790, "results": "274", "hashOfConfig": "246"}, {"size": 1695, "mtime": 1747186871230, "results": "275", "hashOfConfig": "257"}, {"size": 3161, "mtime": 1747232764014, "results": "276", "hashOfConfig": "257"}, {"size": 1533, "mtime": 1746252541007, "results": "277", "hashOfConfig": "246"}, {"size": 5667, "mtime": 1747280262786, "results": "278", "hashOfConfig": "246"}, {"size": 12973, "mtime": 1747224498475, "results": "279", "hashOfConfig": "246"}, {"size": 5107, "mtime": 1747280292414, "results": "280", "hashOfConfig": "246"}, {"size": 2663, "mtime": 1746252696882, "results": "281", "hashOfConfig": "246"}, {"size": 6747, "mtime": 1747280339749, "results": "282", "hashOfConfig": "246"}, {"size": 5713, "mtime": 1745981758114, "results": "283", "hashOfConfig": "246"}, {"size": 675, "mtime": 1745976791748, "results": "284", "hashOfConfig": "257"}, {"size": 1777, "mtime": 1746254015165, "results": "285", "hashOfConfig": "246"}, {"size": 4103, "mtime": 1746200520123, "results": "286", "hashOfConfig": "257"}, {"size": 7914, "mtime": 1746251582912, "results": "287", "hashOfConfig": "246"}, {"size": 7222, "mtime": 1746249216418, "results": "288", "hashOfConfig": "246"}, {"size": 9844, "mtime": 1747157743264, "results": "289", "hashOfConfig": "246"}, {"size": 3866, "mtime": 1747221404207, "results": "290", "hashOfConfig": "257"}, {"size": 2535, "mtime": 1745978386143, "results": "291", "hashOfConfig": "246"}, {"size": 10837, "mtime": 1747184495163, "results": "292", "hashOfConfig": "246"}, {"size": 1105, "mtime": 1746201832350, "results": "293", "hashOfConfig": "246"}, {"size": 3877, "mtime": 1747235933700, "results": "294", "hashOfConfig": "246"}, {"size": 23349, "mtime": 1747184111024, "results": "295", "hashOfConfig": "246"}, {"size": 1689, "mtime": 1745982730905, "results": "296", "hashOfConfig": "246"}, {"size": 3697, "mtime": 1747184461868, "results": "297", "hashOfConfig": "257"}, {"size": 959, "mtime": 1747186815101, "results": "298", "hashOfConfig": "257"}, {"size": 5906, "mtime": 1752248869494, "results": "299", "hashOfConfig": "246"}, {"size": 2847, "mtime": 1747187027857, "results": "300", "hashOfConfig": "246"}, {"size": 2604, "mtime": 1747279467729, "results": "301", "hashOfConfig": "246"}, {"size": 8119, "mtime": 1747220020952, "results": "302", "hashOfConfig": "257"}, {"size": 8243, "mtime": 1747220059414, "results": "303", "hashOfConfig": "257"}, {"size": 3071, "mtime": 1747221577347, "results": "304", "hashOfConfig": "257"}, {"size": 6125, "mtime": 1747221750779, "results": "305", "hashOfConfig": "257"}, {"size": 6017, "mtime": 1747221715802, "results": "306", "hashOfConfig": "257"}, {"size": 3890, "mtime": 1747221780672, "results": "307", "hashOfConfig": "257"}, {"size": 3377, "mtime": 1747221613654, "results": "308", "hashOfConfig": "257"}, {"size": 3156, "mtime": 1747221640258, "results": "309", "hashOfConfig": "257"}, {"size": 7752, "mtime": 1747237735157, "results": "310", "hashOfConfig": "246"}, {"size": 11148, "mtime": 1747241866444, "results": "311", "hashOfConfig": "246"}, {"size": 9820, "mtime": 1747279005156, "results": "312", "hashOfConfig": "246"}, {"size": 8196, "mtime": 1747279261336, "results": "313", "hashOfConfig": "246"}, {"size": 9024, "mtime": 1747241959417, "results": "314", "hashOfConfig": "246"}, {"size": 9620, "mtime": 1747242054549, "results": "315", "hashOfConfig": "246"}, {"size": 9567, "mtime": 1747242099457, "results": "316", "hashOfConfig": "246"}, {"size": 5262, "mtime": 1747242002891, "results": "317", "hashOfConfig": "246"}, {"size": 5327, "mtime": 1747280365842, "results": "318", "hashOfConfig": "246"}, {"size": 28904, "mtime": 1752248907873, "results": "319", "hashOfConfig": "246"}, {"size": 1536, "mtime": 1747237627552, "results": "320", "hashOfConfig": "246"}, {"size": 2783, "mtime": 1747275467037, "results": "321", "hashOfConfig": "246"}, {"size": 4730, "mtime": 1747275582856, "results": "322", "hashOfConfig": "246"}, {"size": 5097, "mtime": 1747302359006, "results": "323", "hashOfConfig": "257"}, {"size": 7775, "mtime": 1747362122128, "results": "324", "hashOfConfig": "246"}, {"size": 9329, "mtime": 1752248933994, "results": "325", "hashOfConfig": "246"}, {"size": 9223, "mtime": 1752248778616, "results": "326", "hashOfConfig": "246"}, {"size": 11164, "mtime": 1747280695673, "results": "327", "hashOfConfig": "246"}, {"size": 1840, "mtime": 1747320082170, "results": "328", "hashOfConfig": "246"}, {"size": 9696, "mtime": 1747280682590, "results": "329", "hashOfConfig": "246"}, {"size": 5157, "mtime": 1747277968202, "results": "330", "hashOfConfig": "246"}, {"size": 2956, "mtime": 1747277488883, "results": "331", "hashOfConfig": "246"}, {"size": 2175, "mtime": 1747277470141, "results": "332", "hashOfConfig": "246"}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["825"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["826", "827", "828"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["829"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["830"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["831", "832"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["833", "834", "835"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["836"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["837", "838"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["839", "840", "841"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["842"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["843"], ["844"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["845"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["846"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["847"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["848"], ["849"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["850"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["851", "852"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["853", "854", "855"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AdminContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["856", "857", "858"], ["859"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["860", "861"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipsManager.tsx", [], ["862", "863", "864"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/MessagesManager.tsx", ["865", "866", "867"], ["868"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/clearStorage.js", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["869", "870", "871", "872"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/testUtils.js", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", ["873"], ["874"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["875"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", [], ["876"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", ["877"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["878"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/BulkScholarshipImport.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["879"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["880"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", ["881"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["882", "883"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["884", "885"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["886"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["887"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/secureApiService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["888"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SecureAdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["889", "890"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthFeedback.tsx", ["891"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/authMigration.ts", ["892", "893", "894"], [], {"ruleId": "895", "severity": 1, "message": "896", "line": 170, "column": 7, "nodeType": "897", "messageId": "898", "endLine": 170, "endColumn": 15}, {"ruleId": "899", "severity": 1, "message": "900", "line": 51, "column": 15, "nodeType": "901", "endLine": 51, "endColumn": 74}, {"ruleId": "899", "severity": 1, "message": "900", "line": 57, "column": 15, "nodeType": "901", "endLine": 57, "endColumn": 74}, {"ruleId": "899", "severity": 1, "message": "900", "line": 63, "column": 15, "nodeType": "901", "endLine": 63, "endColumn": 74}, {"ruleId": "895", "severity": 1, "message": "896", "line": 170, "column": 7, "nodeType": "897", "messageId": "898", "endLine": 170, "endColumn": 15}, {"ruleId": "902", "severity": 1, "message": "903", "line": 44, "column": 6, "nodeType": "904", "endLine": 44, "endColumn": 68, "suggestions": "905"}, {"ruleId": "895", "severity": 1, "message": "906", "line": 3, "column": 8, "nodeType": "897", "messageId": "898", "endLine": 3, "endColumn": 19}, {"ruleId": "895", "severity": 1, "message": "907", "line": 31, "column": 11, "nodeType": "897", "messageId": "898", "endLine": 31, "endColumn": 23}, {"ruleId": "899", "severity": 1, "message": "900", "line": 51, "column": 15, "nodeType": "901", "endLine": 51, "endColumn": 74}, {"ruleId": "899", "severity": 1, "message": "900", "line": 57, "column": 15, "nodeType": "901", "endLine": 57, "endColumn": 74}, {"ruleId": "899", "severity": 1, "message": "900", "line": 63, "column": 15, "nodeType": "901", "endLine": 63, "endColumn": 74}, {"ruleId": "895", "severity": 1, "message": "907", "line": 34, "column": 11, "nodeType": "897", "messageId": "898", "endLine": 34, "endColumn": 23}, {"ruleId": "895", "severity": 1, "message": "908", "line": 10, "column": 3, "nodeType": "897", "messageId": "898", "endLine": 10, "endColumn": 8}, {"ruleId": "895", "severity": 1, "message": "909", "line": 15, "column": 24, "nodeType": "897", "messageId": "898", "endLine": 15, "endColumn": 43}, {"ruleId": "895", "severity": 1, "message": "910", "line": 3, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 3, "endColumn": 21}, {"ruleId": "895", "severity": 1, "message": "911", "line": 44, "column": 9, "nodeType": "897", "messageId": "898", "endLine": 44, "endColumn": 17}, {"ruleId": "912", "severity": 1, "message": "913", "line": 162, "column": 36, "nodeType": "914", "messageId": "915", "endLine": 162, "endColumn": 60}, {"ruleId": "902", "severity": 1, "message": "916", "line": 37, "column": 6, "nodeType": "904", "endLine": 37, "endColumn": 42, "suggestions": "917", "suppressions": "918"}, {"ruleId": "895", "severity": 1, "message": "919", "line": 236, "column": 9, "nodeType": "897", "messageId": "898", "endLine": 236, "endColumn": 30}, {"ruleId": "902", "severity": 1, "message": "916", "line": 64, "column": 6, "nodeType": "904", "endLine": 64, "endColumn": 61, "suggestions": "920", "suppressions": "921"}, {"ruleId": "902", "severity": 1, "message": "916", "line": 29, "column": 6, "nodeType": "904", "endLine": 29, "endColumn": 31, "suggestions": "922", "suppressions": "923"}, {"ruleId": "902", "severity": 1, "message": "924", "line": 25, "column": 6, "nodeType": "904", "endLine": 25, "endColumn": 8, "suggestions": "925", "suppressions": "926"}, {"ruleId": "902", "severity": 1, "message": "927", "line": 177, "column": 6, "nodeType": "904", "endLine": 177, "endColumn": 8, "suggestions": "928", "suppressions": "929"}, {"ruleId": "895", "severity": 1, "message": "910", "line": 3, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 3, "endColumn": 21}, {"ruleId": "902", "severity": 1, "message": "930", "line": 37, "column": 6, "nodeType": "904", "endLine": 37, "endColumn": 8, "suggestions": "931", "suppressions": "932"}, {"ruleId": "902", "severity": 1, "message": "903", "line": 44, "column": 6, "nodeType": "904", "endLine": 44, "endColumn": 68, "suggestions": "933"}, {"ruleId": "899", "severity": 1, "message": "900", "line": 135, "column": 21, "nodeType": "901", "endLine": 135, "endColumn": 96}, {"ruleId": "899", "severity": 1, "message": "900", "line": 141, "column": 21, "nodeType": "901", "endLine": 141, "endColumn": 96}, {"ruleId": "899", "severity": 1, "message": "900", "line": 108, "column": 21, "nodeType": "901", "endLine": 108, "endColumn": 94}, {"ruleId": "899", "severity": 1, "message": "900", "line": 114, "column": 21, "nodeType": "901", "endLine": 114, "endColumn": 94}, {"ruleId": "899", "severity": 1, "message": "900", "line": 120, "column": 21, "nodeType": "901", "endLine": 120, "endColumn": 94}, {"ruleId": "895", "severity": 1, "message": "934", "line": 2, "column": 105, "nodeType": "897", "messageId": "898", "endLine": 2, "endColumn": 111}, {"ruleId": "895", "severity": 1, "message": "935", "line": 3, "column": 111, "nodeType": "897", "messageId": "898", "endLine": 3, "endColumn": 126}, {"ruleId": "895", "severity": 1, "message": "910", "line": 4, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 4, "endColumn": 21}, {"ruleId": "902", "severity": 1, "message": "916", "line": 45, "column": 6, "nodeType": "904", "endLine": 45, "endColumn": 31, "suggestions": "936", "suppressions": "937"}, {"ruleId": "895", "severity": 1, "message": "911", "line": 44, "column": 9, "nodeType": "897", "messageId": "898", "endLine": 44, "endColumn": 17}, {"ruleId": "902", "severity": 1, "message": "938", "line": 136, "column": 6, "nodeType": "904", "endLine": 136, "endColumn": 8, "suggestions": "939"}, {"ruleId": "895", "severity": 1, "message": "940", "line": 43, "column": 11, "nodeType": "897", "messageId": "898", "endLine": 43, "endColumn": 22, "suppressions": "941"}, {"ruleId": "902", "severity": 1, "message": "903", "line": 122, "column": 6, "nodeType": "904", "endLine": 122, "endColumn": 29, "suggestions": "942", "suppressions": "943"}, {"ruleId": "902", "severity": 1, "message": "916", "line": 135, "column": 6, "nodeType": "904", "endLine": 135, "endColumn": 61, "suggestions": "944", "suppressions": "945"}, {"ruleId": "895", "severity": 1, "message": "946", "line": 3, "column": 56, "nodeType": "897", "messageId": "898", "endLine": 3, "endColumn": 70}, {"ruleId": "895", "severity": 1, "message": "947", "line": 5, "column": 15, "nodeType": "897", "messageId": "898", "endLine": 5, "endColumn": 31}, {"ruleId": "895", "severity": 1, "message": "948", "line": 41, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 41, "endColumn": 20}, {"ruleId": "902", "severity": 1, "message": "916", "line": 50, "column": 6, "nodeType": "904", "endLine": 50, "endColumn": 79, "suggestions": "949", "suppressions": "950"}, {"ruleId": "899", "severity": 1, "message": "900", "line": 117, "column": 17, "nodeType": "901", "endLine": 117, "endColumn": 131}, {"ruleId": "899", "severity": 1, "message": "900", "line": 125, "column": 17, "nodeType": "901", "endLine": 125, "endColumn": 131}, {"ruleId": "899", "severity": 1, "message": "900", "line": 133, "column": 17, "nodeType": "901", "endLine": 133, "endColumn": 131}, {"ruleId": "899", "severity": 1, "message": "900", "line": 141, "column": 17, "nodeType": "901", "endLine": 141, "endColumn": 131}, {"ruleId": "895", "severity": 1, "message": "910", "line": 3, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 3, "endColumn": 21}, {"ruleId": "902", "severity": 1, "message": "930", "line": 37, "column": 6, "nodeType": "904", "endLine": 37, "endColumn": 8, "suggestions": "951", "suppressions": "952"}, {"ruleId": "902", "severity": 1, "message": "924", "line": 25, "column": 6, "nodeType": "904", "endLine": 25, "endColumn": 8, "suggestions": "953", "suppressions": "954"}, {"ruleId": "902", "severity": 1, "message": "927", "line": 128, "column": 6, "nodeType": "904", "endLine": 128, "endColumn": 8, "suggestions": "955", "suppressions": "956"}, {"ruleId": "895", "severity": 1, "message": "907", "line": 26, "column": 11, "nodeType": "897", "messageId": "898", "endLine": 26, "endColumn": 23}, {"ruleId": "899", "severity": 1, "message": "900", "line": 101, "column": 26, "nodeType": "901", "endLine": 101, "endColumn": 87}, {"ruleId": "895", "severity": 1, "message": "957", "line": 2, "column": 40, "nodeType": "897", "messageId": "898", "endLine": 2, "endColumn": 44}, {"ruleId": "902", "severity": 1, "message": "958", "line": 38, "column": 6, "nodeType": "904", "endLine": 38, "endColumn": 8, "suggestions": "959"}, {"ruleId": "895", "severity": 1, "message": "960", "line": 25, "column": 9, "nodeType": "897", "messageId": "898", "endLine": 25, "endColumn": 21}, {"ruleId": "899", "severity": 1, "message": "900", "line": 186, "column": 21, "nodeType": "901", "endLine": 186, "endColumn": 74}, {"ruleId": "899", "severity": 1, "message": "900", "line": 188, "column": 21, "nodeType": "901", "endLine": 188, "endColumn": 74}, {"ruleId": "895", "severity": 1, "message": "957", "line": 4, "column": 30, "nodeType": "897", "messageId": "898", "endLine": 4, "endColumn": 34}, {"ruleId": "895", "severity": 1, "message": "961", "line": 7, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 7, "endColumn": 16}, {"ruleId": "962", "severity": 1, "message": "963", "line": 172, "column": 1, "nodeType": "964", "endLine": 179, "endColumn": 3}, {"ruleId": "895", "severity": 1, "message": "961", "line": 2, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 2, "endColumn": 16}, {"ruleId": "902", "severity": 1, "message": "965", "line": 43, "column": 6, "nodeType": "904", "endLine": 43, "endColumn": 26, "suggestions": "966"}, {"ruleId": "895", "severity": 1, "message": "967", "line": 13, "column": 10, "nodeType": "897", "messageId": "898", "endLine": 13, "endColumn": 20}, {"ruleId": "902", "severity": 1, "message": "968", "line": 105, "column": 6, "nodeType": "904", "endLine": 105, "endColumn": 8, "suggestions": "969"}, {"ruleId": "895", "severity": 1, "message": "970", "line": 5, "column": 15, "nodeType": "897", "messageId": "898", "endLine": 5, "endColumn": 20}, {"ruleId": "895", "severity": 1, "message": "971", "line": 8, "column": 8, "nodeType": "897", "messageId": "898", "endLine": 8, "endColumn": 24}, {"ruleId": "895", "severity": 1, "message": "972", "line": 50, "column": 11, "nodeType": "897", "messageId": "898", "endLine": 50, "endColumn": 16}, {"ruleId": "962", "severity": 1, "message": "963", "line": 76, "column": 1, "nodeType": "964", "endLine": 80, "endColumn": 3}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["973"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["974"], ["975"], "'sendEmailNotification' is assigned a value but never used.", ["976"], ["977"], ["978"], ["979"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["980"], ["981"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["982"], ["983"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["984"], ["985"], ["986"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["987"], ["988"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["989"], "'ApiResponse' is defined but never used.", ["990"], ["991"], ["992"], ["993"], ["994"], "'FilterOutlined' is defined but never used.", "'RangePickerProps' is defined but never used.", "'bulkAction' is assigned a value but never used.", ["995"], ["996"], ["997"], ["998"], ["999"], ["1000"], ["1001"], ["1002"], "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["1003"], "'scrollToTabs' is assigned a value but never used.", "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'verifyToken'. Either include it or remove the dependency array.", ["1004"], "'DateFormat' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["1005"], "'Title' is assigned a value but never used.", "'secureApiService' is defined but never used.", "'admin' is assigned a value but never used.", {"desc": "1006", "fix": "1007"}, {"desc": "1008", "fix": "1009"}, {"kind": "1010", "justification": "1011"}, {"desc": "1012", "fix": "1013"}, {"kind": "1010", "justification": "1011"}, {"desc": "1014", "fix": "1015"}, {"kind": "1010", "justification": "1011"}, {"desc": "1016", "fix": "1017"}, {"kind": "1010", "justification": "1011"}, {"desc": "1018", "fix": "1019"}, {"kind": "1010", "justification": "1011"}, {"desc": "1020", "fix": "1021"}, {"kind": "1010", "justification": "1011"}, {"desc": "1006", "fix": "1022"}, {"desc": "1014", "fix": "1023"}, {"kind": "1010", "justification": "1011"}, {"desc": "1024", "fix": "1025"}, {"kind": "1010", "justification": "1011"}, {"desc": "1026", "fix": "1027"}, {"kind": "1010", "justification": "1011"}, {"desc": "1012", "fix": "1028"}, {"kind": "1010", "justification": "1011"}, {"desc": "1029", "fix": "1030"}, {"kind": "1010", "justification": "1011"}, {"desc": "1020", "fix": "1031"}, {"kind": "1010", "justification": "1011"}, {"desc": "1016", "fix": "1032"}, {"kind": "1010", "justification": "1011"}, {"desc": "1018", "fix": "1033"}, {"kind": "1010", "justification": "1011"}, {"desc": "1034", "fix": "1035"}, {"desc": "1036", "fix": "1037"}, {"desc": "1038", "fix": "1039"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "1040", "text": "1041"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "1042", "text": "1043"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "1044", "text": "1045"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "1046", "text": "1047"}, "Update the dependencies array to be: [initializeSetup]", {"range": "1048", "text": "1049"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "1050", "text": "1051"}, "Update the dependencies array to be: [fetchSettings]", {"range": "1052", "text": "1053"}, {"range": "1054", "text": "1041"}, {"range": "1055", "text": "1047"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "1056", "text": "1057"}, "Update the dependencies array to be: [currentPage, fetchScholarships, pageSize]", {"range": "1058", "text": "1059"}, {"range": "1060", "text": "1045"}, "Update the dependencies array to be: [messages, searchTerm, statusFilter, dateRange, sortField, sortDirection, applyFilters]", {"range": "1061", "text": "1062"}, {"range": "1063", "text": "1053"}, {"range": "1064", "text": "1049"}, {"range": "1065", "text": "1051"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "1066", "text": "1067"}, "Update the dependencies array to be: [token, accountType, verifyToken]", {"range": "1068", "text": "1069"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "1070", "text": "1071"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [1228, 1290], [1968, 1993], [4624, 4626], "[adminInfo, stats]", [3529, 3552], "[currentPage, fetchScholarships, pageSize]", [3917, 3972], [2203, 2276], "[messages, searchTerm, statusFilter, dateRange, sortField, sortDirection, applyFilters]", [1255, 1257], [969, 971], [4548, 4550], [1326, 1328], "[backgrounds.length]", [1455, 1475], "[token, accountType, verifyToken]", [3161, 3163], "[fetchSecurityEvents]"]