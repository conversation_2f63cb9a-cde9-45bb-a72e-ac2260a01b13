[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AuthContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AdminContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdminProtectedRoute.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLoginTester.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipsManager.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/MessagesManager.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/clearStorage.js": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/testUtils.js": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/apiConfig.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorVerification.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/BulkScholarshipImport.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "152", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "153", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "154", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "155", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx": "156", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/secureApiService.ts": "157", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx": "158", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "159", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SecureAdminProtectedRoute.tsx": "160", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "161", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthFeedback.tsx": "162", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx": "163", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/authMigration.ts": "164", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthDebugger.tsx": "165"}, {"size": 274, "mtime": *************, "results": "166", "hashOfConfig": "167"}, {"size": 2118, "mtime": *************, "results": "168", "hashOfConfig": "167"}, {"size": 5438, "mtime": *************, "results": "169", "hashOfConfig": "167"}, {"size": 10242, "mtime": *************, "results": "170", "hashOfConfig": "167"}, {"size": 1742, "mtime": *************, "results": "171", "hashOfConfig": "167"}, {"size": 7131, "mtime": 1745981562258, "results": "172", "hashOfConfig": "167"}, {"size": 10498, "mtime": 1745982258490, "results": "173", "hashOfConfig": "167"}, {"size": 3076, "mtime": 1745945973317, "results": "174", "hashOfConfig": "167"}, {"size": 553, "mtime": 1745978322072, "results": "175", "hashOfConfig": "167"}, {"size": 6250, "mtime": 1745977773426, "results": "176", "hashOfConfig": "167"}, {"size": 2446, "mtime": 1745945132621, "results": "177", "hashOfConfig": "167"}, {"size": 2645, "mtime": 1745977581979, "results": "178", "hashOfConfig": "167"}, {"size": 4167, "mtime": 1745981843303, "results": "179", "hashOfConfig": "167"}, {"size": 5713, "mtime": 1745981758114, "results": "180", "hashOfConfig": "167"}, {"size": 675, "mtime": 1745976791748, "results": "181", "hashOfConfig": "167"}, {"size": 1059, "mtime": 1745976720607, "results": "182", "hashOfConfig": "167"}, {"size": 3452, "mtime": 1745946003719, "results": "183", "hashOfConfig": "167"}, {"size": 2518, "mtime": 1745983866923, "results": "184", "hashOfConfig": "167"}, {"size": 1737, "mtime": 1745978376608, "results": "185", "hashOfConfig": "167"}, {"size": 4075, "mtime": 1745982269507, "results": "186", "hashOfConfig": "167"}, {"size": 4531, "mtime": 1745982263875, "results": "187", "hashOfConfig": "167"}, {"size": 5406, "mtime": 1745982274929, "results": "188", "hashOfConfig": "167"}, {"size": 2535, "mtime": 1745978386143, "results": "189", "hashOfConfig": "167"}, {"size": 737, "mtime": 1745944438688, "results": "190", "hashOfConfig": "167"}, {"size": 2323, "mtime": 1745982233889, "results": "191", "hashOfConfig": "167"}, {"size": 274, "mtime": *************, "results": "192", "hashOfConfig": "193"}, {"size": 4324, "mtime": 1746276088446, "results": "194", "hashOfConfig": "193"}, {"size": 7131, "mtime": 1745981562258, "results": "195", "hashOfConfig": "193"}, {"size": 10498, "mtime": 1745982258490, "results": "196", "hashOfConfig": "193"}, {"size": 10242, "mtime": *************, "results": "197", "hashOfConfig": "193"}, {"size": 1742, "mtime": *************, "results": "198", "hashOfConfig": "193"}, {"size": 8315, "mtime": 1746204095547, "results": "199", "hashOfConfig": "193"}, {"size": 3076, "mtime": 1745945973317, "results": "200", "hashOfConfig": "193"}, {"size": 2535, "mtime": 1746033992389, "results": "201", "hashOfConfig": "193"}, {"size": 1737, "mtime": 1745978376608, "results": "202", "hashOfConfig": "193"}, {"size": 6250, "mtime": 1745977773426, "results": "203", "hashOfConfig": "193"}, {"size": 553, "mtime": 1745978322072, "results": "204", "hashOfConfig": "193"}, {"size": 2446, "mtime": 1745945132621, "results": "205", "hashOfConfig": "193"}, {"size": 5154, "mtime": 1746276100698, "results": "206", "hashOfConfig": "193"}, {"size": 8097, "mtime": 1745983506688, "results": "207", "hashOfConfig": "193"}, {"size": 6024, "mtime": 1746026061709, "results": "208", "hashOfConfig": "193"}, {"size": 9559, "mtime": 1746270350218, "results": "209", "hashOfConfig": "193"}, {"size": 737, "mtime": 1745944438688, "results": "210", "hashOfConfig": "193"}, {"size": 2645, "mtime": 1745977581979, "results": "211", "hashOfConfig": "193"}, {"size": 4531, "mtime": 1745982263875, "results": "212", "hashOfConfig": "193"}, {"size": 4075, "mtime": 1745982269507, "results": "213", "hashOfConfig": "193"}, {"size": 5406, "mtime": 1745982274929, "results": "214", "hashOfConfig": "193"}, {"size": 5713, "mtime": 1745981758114, "results": "215", "hashOfConfig": "193"}, {"size": 675, "mtime": 1745976791748, "results": "216", "hashOfConfig": "193"}, {"size": 4167, "mtime": 1745981843303, "results": "217", "hashOfConfig": "193"}, {"size": 2115, "mtime": 1746029576846, "results": "218", "hashOfConfig": "193"}, {"size": 1689, "mtime": 1745982730905, "results": "219", "hashOfConfig": "193"}, {"size": 19288, "mtime": 1745983400384, "results": "220", "hashOfConfig": "193"}, {"size": 1059, "mtime": 1745976720607, "results": "221", "hashOfConfig": "193"}, {"size": 2535, "mtime": 1745978386143, "results": "222", "hashOfConfig": "193"}, {"size": 14052, "mtime": 1746274142489, "results": "223", "hashOfConfig": "193"}, {"size": 3939, "mtime": 1746017528736, "results": "224", "hashOfConfig": "193"}, {"size": 13857, "mtime": 1746282401482, "results": "225", "hashOfConfig": "193"}, {"size": 11940, "mtime": 1746252382064, "results": "226", "hashOfConfig": "193"}, {"size": 38877, "mtime": 1746252407519, "results": "227", "hashOfConfig": "193"}, {"size": 8393, "mtime": 1746249939564, "results": "228", "hashOfConfig": "193"}, {"size": 11544, "mtime": 1746272209267, "results": "229", "hashOfConfig": "193"}, {"size": 1343, "mtime": 1746033436995, "results": "230", "hashOfConfig": "193"}, {"size": 1752, "mtime": 1746274101102, "results": "231", "hashOfConfig": "193"}, {"size": 3341, "mtime": 1746199132190, "results": "232", "hashOfConfig": "193"}, {"size": 7045, "mtime": 1746199160974, "results": "233", "hashOfConfig": "193"}, {"size": 4103, "mtime": 1746200520123, "results": "234", "hashOfConfig": "193"}, {"size": 6276, "mtime": 1746249196201, "results": "235", "hashOfConfig": "193"}, {"size": 7222, "mtime": 1746249216418, "results": "236", "hashOfConfig": "193"}, {"size": 1105, "mtime": 1746201832350, "results": "237", "hashOfConfig": "193"}, {"size": 921, "mtime": 1746202207790, "results": "238", "hashOfConfig": "193"}, {"size": 11379, "mtime": 1746276323665, "results": "239", "hashOfConfig": "193"}, {"size": 1059, "mtime": 1746226321253, "results": "240", "hashOfConfig": "193"}, {"size": 7914, "mtime": 1746251582912, "results": "241", "hashOfConfig": "193"}, {"size": 4784, "mtime": 1746252717773, "results": "242", "hashOfConfig": "193"}, {"size": 1777, "mtime": 1746254015165, "results": "243", "hashOfConfig": "193"}, {"size": 9435, "mtime": 1746252638103, "results": "244", "hashOfConfig": "193"}, {"size": 5504, "mtime": 1746275135511, "results": "245", "hashOfConfig": "193"}, {"size": 620, "mtime": 1747275499301, "results": "246", "hashOfConfig": "247"}, {"size": 5228, "mtime": 1747278552152, "results": "248", "hashOfConfig": "247"}, {"size": 13828, "mtime": 1752248890943, "results": "249", "hashOfConfig": "247"}, {"size": 19195, "mtime": 1747280789154, "results": "250", "hashOfConfig": "247"}, {"size": 3587, "mtime": 1747235972243, "results": "251", "hashOfConfig": "247"}, {"size": 10653, "mtime": 1747235897205, "results": "252", "hashOfConfig": "247"}, {"size": 2535, "mtime": 1746033992389, "results": "253", "hashOfConfig": "247"}, {"size": 8578, "mtime": 1746253995466, "results": "254", "hashOfConfig": "247"}, {"size": 3076, "mtime": 1745945973317, "results": "255", "hashOfConfig": "247"}, {"size": 1974, "mtime": 1747278879917, "results": "256", "hashOfConfig": "247"}, {"size": 1752, "mtime": 1746274101102, "results": "257", "hashOfConfig": "258"}, {"size": 553, "mtime": 1745978322072, "results": "259", "hashOfConfig": "247"}, {"size": 18635, "mtime": 1747235596822, "results": "260", "hashOfConfig": "258"}, {"size": 2446, "mtime": 1745945132621, "results": "261", "hashOfConfig": "247"}, {"size": 4784, "mtime": 1746252717773, "results": "262", "hashOfConfig": "247"}, {"size": 19928, "mtime": 1747184532558, "results": "263", "hashOfConfig": "247"}, {"size": 5619, "mtime": 1747278582985, "results": "264", "hashOfConfig": "247"}, {"size": 5504, "mtime": 1746275135511, "results": "265", "hashOfConfig": "247"}, {"size": 11544, "mtime": 1746272209267, "results": "266", "hashOfConfig": "247"}, {"size": 14051, "mtime": 1747277440650, "results": "267", "hashOfConfig": "247"}, {"size": 14143, "mtime": 1747150652537, "results": "268", "hashOfConfig": "247"}, {"size": 7045, "mtime": 1746199160974, "results": "269", "hashOfConfig": "247"}, {"size": 48926, "mtime": 1747181396095, "results": "270", "hashOfConfig": "247"}, {"size": 3341, "mtime": 1746199132190, "results": "271", "hashOfConfig": "247"}, {"size": 25795, "mtime": 1747183272913, "results": "272", "hashOfConfig": "247"}, {"size": 1059, "mtime": 1746226321253, "results": "273", "hashOfConfig": "247"}, {"size": 6276, "mtime": 1746249196201, "results": "274", "hashOfConfig": "247"}, {"size": 921, "mtime": 1746202207790, "results": "275", "hashOfConfig": "247"}, {"size": 1695, "mtime": 1747186871230, "results": "276", "hashOfConfig": "258"}, {"size": 3161, "mtime": 1747232764014, "results": "277", "hashOfConfig": "258"}, {"size": 1533, "mtime": 1746252541007, "results": "278", "hashOfConfig": "247"}, {"size": 5667, "mtime": 1747280262786, "results": "279", "hashOfConfig": "247"}, {"size": 12973, "mtime": 1747224498475, "results": "280", "hashOfConfig": "247"}, {"size": 5107, "mtime": 1747280292414, "results": "281", "hashOfConfig": "247"}, {"size": 2663, "mtime": 1746252696882, "results": "282", "hashOfConfig": "247"}, {"size": 6747, "mtime": 1747280339749, "results": "283", "hashOfConfig": "247"}, {"size": 5713, "mtime": 1745981758114, "results": "284", "hashOfConfig": "247"}, {"size": 675, "mtime": 1745976791748, "results": "285", "hashOfConfig": "258"}, {"size": 1777, "mtime": 1746254015165, "results": "286", "hashOfConfig": "247"}, {"size": 4103, "mtime": 1746200520123, "results": "287", "hashOfConfig": "258"}, {"size": 7914, "mtime": 1746251582912, "results": "288", "hashOfConfig": "247"}, {"size": 7222, "mtime": 1746249216418, "results": "289", "hashOfConfig": "247"}, {"size": 9844, "mtime": 1747157743264, "results": "290", "hashOfConfig": "247"}, {"size": 3866, "mtime": 1747221404207, "results": "291", "hashOfConfig": "258"}, {"size": 2535, "mtime": 1745978386143, "results": "292", "hashOfConfig": "247"}, {"size": 10837, "mtime": 1747184495163, "results": "293", "hashOfConfig": "247"}, {"size": 1105, "mtime": 1746201832350, "results": "294", "hashOfConfig": "247"}, {"size": 3877, "mtime": 1747235933700, "results": "295", "hashOfConfig": "247"}, {"size": 23349, "mtime": 1747184111024, "results": "296", "hashOfConfig": "247"}, {"size": 1689, "mtime": 1745982730905, "results": "297", "hashOfConfig": "247"}, {"size": 3697, "mtime": 1747184461868, "results": "298", "hashOfConfig": "258"}, {"size": 959, "mtime": 1747186815101, "results": "299", "hashOfConfig": "258"}, {"size": 5906, "mtime": 1752248869494, "results": "300", "hashOfConfig": "247"}, {"size": 2847, "mtime": 1747187027857, "results": "301", "hashOfConfig": "247"}, {"size": 2604, "mtime": 1747279467729, "results": "302", "hashOfConfig": "247"}, {"size": 8119, "mtime": 1747220020952, "results": "303", "hashOfConfig": "258"}, {"size": 8243, "mtime": 1747220059414, "results": "304", "hashOfConfig": "258"}, {"size": 3071, "mtime": 1747221577347, "results": "305", "hashOfConfig": "258"}, {"size": 6125, "mtime": 1747221750779, "results": "306", "hashOfConfig": "258"}, {"size": 6017, "mtime": 1747221715802, "results": "307", "hashOfConfig": "258"}, {"size": 3890, "mtime": 1747221780672, "results": "308", "hashOfConfig": "258"}, {"size": 3377, "mtime": 1747221613654, "results": "309", "hashOfConfig": "258"}, {"size": 3156, "mtime": 1747221640258, "results": "310", "hashOfConfig": "258"}, {"size": 7752, "mtime": 1747237735157, "results": "311", "hashOfConfig": "247"}, {"size": 11148, "mtime": 1747241866444, "results": "312", "hashOfConfig": "247"}, {"size": 9820, "mtime": 1747279005156, "results": "313", "hashOfConfig": "247"}, {"size": 8196, "mtime": 1747279261336, "results": "314", "hashOfConfig": "247"}, {"size": 9024, "mtime": 1747241959417, "results": "315", "hashOfConfig": "247"}, {"size": 9620, "mtime": 1747242054549, "results": "316", "hashOfConfig": "247"}, {"size": 9567, "mtime": 1747242099457, "results": "317", "hashOfConfig": "247"}, {"size": 5262, "mtime": 1747242002891, "results": "318", "hashOfConfig": "247"}, {"size": 5327, "mtime": 1747280365842, "results": "319", "hashOfConfig": "247"}, {"size": 28904, "mtime": 1752248907873, "results": "320", "hashOfConfig": "247"}, {"size": 1536, "mtime": 1747237627552, "results": "321", "hashOfConfig": "247"}, {"size": 2783, "mtime": 1747275467037, "results": "322", "hashOfConfig": "247"}, {"size": 4730, "mtime": 1747275582856, "results": "323", "hashOfConfig": "247"}, {"size": 5097, "mtime": 1747302359006, "results": "324", "hashOfConfig": "258"}, {"size": 7775, "mtime": 1747362122128, "results": "325", "hashOfConfig": "247"}, {"size": 9046, "mtime": 1752250518120, "results": "326", "hashOfConfig": "247"}, {"size": 9481, "mtime": 1752250083763, "results": "327", "hashOfConfig": "247"}, {"size": 11164, "mtime": 1747280695673, "results": "328", "hashOfConfig": "247"}, {"size": 1840, "mtime": 1747320082170, "results": "329", "hashOfConfig": "247"}, {"size": 9696, "mtime": 1747280682590, "results": "330", "hashOfConfig": "247"}, {"size": 5157, "mtime": 1747277968202, "results": "331", "hashOfConfig": "247"}, {"size": 2956, "mtime": 1747277488883, "results": "332", "hashOfConfig": "247"}, {"size": 2175, "mtime": 1747277470141, "results": "333", "hashOfConfig": "247"}, {"size": 4796, "mtime": 1752250600870, "results": "334", "hashOfConfig": "247"}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["830"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["831", "832", "833"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["834"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["835"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["836", "837"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["838", "839", "840"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["841"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["842", "843"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["844", "845", "846"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["847"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["848"], ["849"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["850"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["851"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["852"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["853"], ["854"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["855"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["856", "857"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["858", "859", "860"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/AdminContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["861", "862", "863"], ["864"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["865", "866"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipsManager.tsx", [], ["867", "868", "869"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/MessagesManager.tsx", ["870", "871", "872"], ["873"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/clearStorage.js", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["874", "875", "876", "877"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/testUtils.js", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", ["878"], ["879"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["880"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", [], ["881"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", ["882"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["883"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/BulkScholarshipImport.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["884"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["885"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", ["886"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["887", "888"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["889", "890"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["891"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["892"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/secureApiService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["893"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SecureAdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["894", "895"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthFeedback.tsx", ["896"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/authMigration.ts", ["897", "898", "899"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthDebugger.tsx", [], [], {"ruleId": "900", "severity": 1, "message": "901", "line": 170, "column": 7, "nodeType": "902", "messageId": "903", "endLine": 170, "endColumn": 15}, {"ruleId": "904", "severity": 1, "message": "905", "line": 51, "column": 15, "nodeType": "906", "endLine": 51, "endColumn": 74}, {"ruleId": "904", "severity": 1, "message": "905", "line": 57, "column": 15, "nodeType": "906", "endLine": 57, "endColumn": 74}, {"ruleId": "904", "severity": 1, "message": "905", "line": 63, "column": 15, "nodeType": "906", "endLine": 63, "endColumn": 74}, {"ruleId": "900", "severity": 1, "message": "901", "line": 170, "column": 7, "nodeType": "902", "messageId": "903", "endLine": 170, "endColumn": 15}, {"ruleId": "907", "severity": 1, "message": "908", "line": 44, "column": 6, "nodeType": "909", "endLine": 44, "endColumn": 68, "suggestions": "910"}, {"ruleId": "900", "severity": 1, "message": "911", "line": 3, "column": 8, "nodeType": "902", "messageId": "903", "endLine": 3, "endColumn": 19}, {"ruleId": "900", "severity": 1, "message": "912", "line": 31, "column": 11, "nodeType": "902", "messageId": "903", "endLine": 31, "endColumn": 23}, {"ruleId": "904", "severity": 1, "message": "905", "line": 51, "column": 15, "nodeType": "906", "endLine": 51, "endColumn": 74}, {"ruleId": "904", "severity": 1, "message": "905", "line": 57, "column": 15, "nodeType": "906", "endLine": 57, "endColumn": 74}, {"ruleId": "904", "severity": 1, "message": "905", "line": 63, "column": 15, "nodeType": "906", "endLine": 63, "endColumn": 74}, {"ruleId": "900", "severity": 1, "message": "912", "line": 34, "column": 11, "nodeType": "902", "messageId": "903", "endLine": 34, "endColumn": 23}, {"ruleId": "900", "severity": 1, "message": "913", "line": 10, "column": 3, "nodeType": "902", "messageId": "903", "endLine": 10, "endColumn": 8}, {"ruleId": "900", "severity": 1, "message": "914", "line": 15, "column": 24, "nodeType": "902", "messageId": "903", "endLine": 15, "endColumn": 43}, {"ruleId": "900", "severity": 1, "message": "915", "line": 3, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 3, "endColumn": 21}, {"ruleId": "900", "severity": 1, "message": "916", "line": 44, "column": 9, "nodeType": "902", "messageId": "903", "endLine": 44, "endColumn": 17}, {"ruleId": "917", "severity": 1, "message": "918", "line": 162, "column": 36, "nodeType": "919", "messageId": "920", "endLine": 162, "endColumn": 60}, {"ruleId": "907", "severity": 1, "message": "921", "line": 37, "column": 6, "nodeType": "909", "endLine": 37, "endColumn": 42, "suggestions": "922", "suppressions": "923"}, {"ruleId": "900", "severity": 1, "message": "924", "line": 236, "column": 9, "nodeType": "902", "messageId": "903", "endLine": 236, "endColumn": 30}, {"ruleId": "907", "severity": 1, "message": "921", "line": 64, "column": 6, "nodeType": "909", "endLine": 64, "endColumn": 61, "suggestions": "925", "suppressions": "926"}, {"ruleId": "907", "severity": 1, "message": "921", "line": 29, "column": 6, "nodeType": "909", "endLine": 29, "endColumn": 31, "suggestions": "927", "suppressions": "928"}, {"ruleId": "907", "severity": 1, "message": "929", "line": 25, "column": 6, "nodeType": "909", "endLine": 25, "endColumn": 8, "suggestions": "930", "suppressions": "931"}, {"ruleId": "907", "severity": 1, "message": "932", "line": 177, "column": 6, "nodeType": "909", "endLine": 177, "endColumn": 8, "suggestions": "933", "suppressions": "934"}, {"ruleId": "900", "severity": 1, "message": "915", "line": 3, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 3, "endColumn": 21}, {"ruleId": "907", "severity": 1, "message": "935", "line": 37, "column": 6, "nodeType": "909", "endLine": 37, "endColumn": 8, "suggestions": "936", "suppressions": "937"}, {"ruleId": "907", "severity": 1, "message": "908", "line": 44, "column": 6, "nodeType": "909", "endLine": 44, "endColumn": 68, "suggestions": "938"}, {"ruleId": "904", "severity": 1, "message": "905", "line": 135, "column": 21, "nodeType": "906", "endLine": 135, "endColumn": 96}, {"ruleId": "904", "severity": 1, "message": "905", "line": 141, "column": 21, "nodeType": "906", "endLine": 141, "endColumn": 96}, {"ruleId": "904", "severity": 1, "message": "905", "line": 108, "column": 21, "nodeType": "906", "endLine": 108, "endColumn": 94}, {"ruleId": "904", "severity": 1, "message": "905", "line": 114, "column": 21, "nodeType": "906", "endLine": 114, "endColumn": 94}, {"ruleId": "904", "severity": 1, "message": "905", "line": 120, "column": 21, "nodeType": "906", "endLine": 120, "endColumn": 94}, {"ruleId": "900", "severity": 1, "message": "939", "line": 2, "column": 105, "nodeType": "902", "messageId": "903", "endLine": 2, "endColumn": 111}, {"ruleId": "900", "severity": 1, "message": "940", "line": 3, "column": 111, "nodeType": "902", "messageId": "903", "endLine": 3, "endColumn": 126}, {"ruleId": "900", "severity": 1, "message": "915", "line": 4, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 4, "endColumn": 21}, {"ruleId": "907", "severity": 1, "message": "921", "line": 45, "column": 6, "nodeType": "909", "endLine": 45, "endColumn": 31, "suggestions": "941", "suppressions": "942"}, {"ruleId": "900", "severity": 1, "message": "916", "line": 44, "column": 9, "nodeType": "902", "messageId": "903", "endLine": 44, "endColumn": 17}, {"ruleId": "907", "severity": 1, "message": "943", "line": 136, "column": 6, "nodeType": "909", "endLine": 136, "endColumn": 8, "suggestions": "944"}, {"ruleId": "900", "severity": 1, "message": "945", "line": 43, "column": 11, "nodeType": "902", "messageId": "903", "endLine": 43, "endColumn": 22, "suppressions": "946"}, {"ruleId": "907", "severity": 1, "message": "908", "line": 122, "column": 6, "nodeType": "909", "endLine": 122, "endColumn": 29, "suggestions": "947", "suppressions": "948"}, {"ruleId": "907", "severity": 1, "message": "921", "line": 135, "column": 6, "nodeType": "909", "endLine": 135, "endColumn": 61, "suggestions": "949", "suppressions": "950"}, {"ruleId": "900", "severity": 1, "message": "951", "line": 3, "column": 56, "nodeType": "902", "messageId": "903", "endLine": 3, "endColumn": 70}, {"ruleId": "900", "severity": 1, "message": "952", "line": 5, "column": 15, "nodeType": "902", "messageId": "903", "endLine": 5, "endColumn": 31}, {"ruleId": "900", "severity": 1, "message": "953", "line": 41, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 41, "endColumn": 20}, {"ruleId": "907", "severity": 1, "message": "921", "line": 50, "column": 6, "nodeType": "909", "endLine": 50, "endColumn": 79, "suggestions": "954", "suppressions": "955"}, {"ruleId": "904", "severity": 1, "message": "905", "line": 117, "column": 17, "nodeType": "906", "endLine": 117, "endColumn": 131}, {"ruleId": "904", "severity": 1, "message": "905", "line": 125, "column": 17, "nodeType": "906", "endLine": 125, "endColumn": 131}, {"ruleId": "904", "severity": 1, "message": "905", "line": 133, "column": 17, "nodeType": "906", "endLine": 133, "endColumn": 131}, {"ruleId": "904", "severity": 1, "message": "905", "line": 141, "column": 17, "nodeType": "906", "endLine": 141, "endColumn": 131}, {"ruleId": "900", "severity": 1, "message": "915", "line": 3, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 3, "endColumn": 21}, {"ruleId": "907", "severity": 1, "message": "935", "line": 37, "column": 6, "nodeType": "909", "endLine": 37, "endColumn": 8, "suggestions": "956", "suppressions": "957"}, {"ruleId": "907", "severity": 1, "message": "929", "line": 25, "column": 6, "nodeType": "909", "endLine": 25, "endColumn": 8, "suggestions": "958", "suppressions": "959"}, {"ruleId": "907", "severity": 1, "message": "932", "line": 128, "column": 6, "nodeType": "909", "endLine": 128, "endColumn": 8, "suggestions": "960", "suppressions": "961"}, {"ruleId": "900", "severity": 1, "message": "912", "line": 26, "column": 11, "nodeType": "902", "messageId": "903", "endLine": 26, "endColumn": 23}, {"ruleId": "904", "severity": 1, "message": "905", "line": 101, "column": 26, "nodeType": "906", "endLine": 101, "endColumn": 87}, {"ruleId": "900", "severity": 1, "message": "962", "line": 2, "column": 40, "nodeType": "902", "messageId": "903", "endLine": 2, "endColumn": 44}, {"ruleId": "907", "severity": 1, "message": "963", "line": 38, "column": 6, "nodeType": "909", "endLine": 38, "endColumn": 8, "suggestions": "964"}, {"ruleId": "900", "severity": 1, "message": "965", "line": 25, "column": 9, "nodeType": "902", "messageId": "903", "endLine": 25, "endColumn": 21}, {"ruleId": "904", "severity": 1, "message": "905", "line": 186, "column": 21, "nodeType": "906", "endLine": 186, "endColumn": 74}, {"ruleId": "904", "severity": 1, "message": "905", "line": 188, "column": 21, "nodeType": "906", "endLine": 188, "endColumn": 74}, {"ruleId": "900", "severity": 1, "message": "962", "line": 4, "column": 30, "nodeType": "902", "messageId": "903", "endLine": 4, "endColumn": 34}, {"ruleId": "900", "severity": 1, "message": "966", "line": 7, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 7, "endColumn": 16}, {"ruleId": "967", "severity": 1, "message": "968", "line": 172, "column": 1, "nodeType": "969", "endLine": 179, "endColumn": 3}, {"ruleId": "900", "severity": 1, "message": "966", "line": 2, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 2, "endColumn": 16}, {"ruleId": "907", "severity": 1, "message": "970", "line": 43, "column": 6, "nodeType": "909", "endLine": 43, "endColumn": 26, "suggestions": "971"}, {"ruleId": "900", "severity": 1, "message": "972", "line": 13, "column": 10, "nodeType": "902", "messageId": "903", "endLine": 13, "endColumn": 20}, {"ruleId": "907", "severity": 1, "message": "973", "line": 105, "column": 6, "nodeType": "909", "endLine": 105, "endColumn": 8, "suggestions": "974"}, {"ruleId": "900", "severity": 1, "message": "975", "line": 5, "column": 15, "nodeType": "902", "messageId": "903", "endLine": 5, "endColumn": 20}, {"ruleId": "900", "severity": 1, "message": "976", "line": 8, "column": 8, "nodeType": "902", "messageId": "903", "endLine": 8, "endColumn": 24}, {"ruleId": "900", "severity": 1, "message": "977", "line": 50, "column": 11, "nodeType": "902", "messageId": "903", "endLine": 50, "endColumn": 16}, {"ruleId": "967", "severity": 1, "message": "968", "line": 76, "column": 1, "nodeType": "969", "endLine": 80, "endColumn": 3}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["978"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["979"], ["980"], "'sendEmailNotification' is assigned a value but never used.", ["981"], ["982"], ["983"], ["984"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["985"], ["986"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["987"], ["988"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["989"], ["990"], ["991"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["992"], ["993"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["994"], "'ApiResponse' is defined but never used.", ["995"], ["996"], ["997"], ["998"], ["999"], "'FilterOutlined' is defined but never used.", "'RangePickerProps' is defined but never used.", "'bulkAction' is assigned a value but never used.", ["1000"], ["1001"], ["1002"], ["1003"], ["1004"], ["1005"], ["1006"], ["1007"], "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["1008"], "'scrollToTabs' is assigned a value but never used.", "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'verifyToken'. Either include it or remove the dependency array.", ["1009"], "'DateFormat' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["1010"], "'Title' is assigned a value but never used.", "'secureApiService' is defined but never used.", "'admin' is assigned a value but never used.", {"desc": "1011", "fix": "1012"}, {"desc": "1013", "fix": "1014"}, {"kind": "1015", "justification": "1016"}, {"desc": "1017", "fix": "1018"}, {"kind": "1015", "justification": "1016"}, {"desc": "1019", "fix": "1020"}, {"kind": "1015", "justification": "1016"}, {"desc": "1021", "fix": "1022"}, {"kind": "1015", "justification": "1016"}, {"desc": "1023", "fix": "1024"}, {"kind": "1015", "justification": "1016"}, {"desc": "1025", "fix": "1026"}, {"kind": "1015", "justification": "1016"}, {"desc": "1011", "fix": "1027"}, {"desc": "1019", "fix": "1028"}, {"kind": "1015", "justification": "1016"}, {"desc": "1029", "fix": "1030"}, {"kind": "1015", "justification": "1016"}, {"desc": "1031", "fix": "1032"}, {"kind": "1015", "justification": "1016"}, {"desc": "1017", "fix": "1033"}, {"kind": "1015", "justification": "1016"}, {"desc": "1034", "fix": "1035"}, {"kind": "1015", "justification": "1016"}, {"desc": "1025", "fix": "1036"}, {"kind": "1015", "justification": "1016"}, {"desc": "1021", "fix": "1037"}, {"kind": "1015", "justification": "1016"}, {"desc": "1023", "fix": "1038"}, {"kind": "1015", "justification": "1016"}, {"desc": "1039", "fix": "1040"}, {"desc": "1041", "fix": "1042"}, {"desc": "1043", "fix": "1044"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "1045", "text": "1046"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "1047", "text": "1048"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "1049", "text": "1050"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "1051", "text": "1052"}, "Update the dependencies array to be: [initializeSetup]", {"range": "1053", "text": "1054"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "1055", "text": "1056"}, "Update the dependencies array to be: [fetchSettings]", {"range": "1057", "text": "1058"}, {"range": "1059", "text": "1046"}, {"range": "1060", "text": "1052"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "1061", "text": "1062"}, "Update the dependencies array to be: [currentPage, fetchScholarships, pageSize]", {"range": "1063", "text": "1064"}, {"range": "1065", "text": "1050"}, "Update the dependencies array to be: [messages, searchTerm, statusFilter, dateRange, sortField, sortDirection, applyFilters]", {"range": "1066", "text": "1067"}, {"range": "1068", "text": "1058"}, {"range": "1069", "text": "1054"}, {"range": "1070", "text": "1056"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "1071", "text": "1072"}, "Update the dependencies array to be: [token, accountType, verifyToken]", {"range": "1073", "text": "1074"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "1075", "text": "1076"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [1228, 1290], [1968, 1993], [4624, 4626], "[adminInfo, stats]", [3529, 3552], "[currentPage, fetchScholarships, pageSize]", [3917, 3972], [2203, 2276], "[messages, searchTerm, statusFilter, dateRange, sortField, sortDirection, applyFilters]", [1255, 1257], [969, 971], [4548, 4550], [1326, 1328], "[backgrounds.length]", [1455, 1475], "[token, accountType, verifyToken]", [3161, 3163], "[fetchSecurityEvents]"]