{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Form, Input, Button, Card, Alert, Typography, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';\nimport { useSecureAuth } from '../../contexts/SecureAuthContext';\nimport secureApiService from '../../services/secureApiService';\nimport AuthFeedback, { AuthStatus } from '../../components/AuthFeedback';\nimport AuthDebugger from '../../components/AuthDebugger';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst SecureLogin = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    adminLogin,\n    isAuthenticated,\n    isAdmin,\n    loading,\n    error,\n    clearError\n  } = useSecureAuth();\n  const [form] = Form.useForm();\n  const [showTwoFactor, setShowTwoFactor] = useState(false);\n  const [twoFactorToken, setTwoFactorToken] = useState('');\n  const [adminId, setAdminId] = useState(null);\n  const [loginError, setLoginError] = useState(null);\n  const [authStatus, setAuthStatus] = useState(AuthStatus.IDLE);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    console.log('Authentication state changed:', {\n      isAuthenticated,\n      isAdmin,\n      loading\n    });\n    if (isAuthenticated && isAdmin && !loading) {\n      console.log('Redirecting to dashboard...');\n      // Use React Router navigation instead of hard redirect\n      navigate('/admin/dashboard');\n    }\n  }, [isAuthenticated, isAdmin, loading, navigate]);\n\n  // Clear errors when component unmounts\n  useEffect(() => {\n    return () => {\n      clearError();\n      setLoginError(null);\n    };\n  }, [clearError]);\n\n  // Handle form submission\n  const onFinish = async values => {\n    try {\n      setLoginError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      console.log('Attempting to login with:', values.email);\n\n      // Test direct API call first\n      try {\n        console.log('Testing direct API call to backend...');\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('API health check successful:', data);\n      } catch (error) {\n        console.error('API health check failed:', error);\n      }\n\n      // Attempt to login\n      await adminLogin(values.email, values.password);\n\n      // If we get here, login was successful\n      console.log('Login successful, setting success state');\n      setAuthStatus(AuthStatus.SUCCESS);\n\n      // Navigate without page reload - the useEffect will handle the redirect\n      console.log('Login successful, waiting for auth state update...');\n    } catch (err) {\n      var _err$response, _err$response2, _err$response2$data;\n      console.error('Login error:', err);\n      // Check if 2FA is required\n      if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 403 && (_err$response2 = err.response) !== null && _err$response2 !== void 0 && (_err$response2$data = _err$response2.data) !== null && _err$response2$data !== void 0 && _err$response2$data.requireTwoFactor) {\n        setShowTwoFactor(true);\n        setAdminId(err.response.data.adminId);\n        setAuthStatus(AuthStatus.INFO);\n      } else {\n        var _err$response3, _err$response3$data;\n        const errorMessage = ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || err.message || 'Login failed';\n        console.error('Setting error state:', errorMessage);\n        setLoginError(errorMessage);\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    }\n  };\n\n  // Handle 2FA verification\n  const handleTwoFactorVerify = async () => {\n    try {\n      if (!adminId) {\n        setLoginError('Admin ID is missing');\n        setAuthStatus(AuthStatus.ERROR);\n        return;\n      }\n      setAuthStatus(AuthStatus.LOADING);\n      const response = await secureApiService.post('/2fa/verify', {\n        adminId,\n        token: twoFactorToken\n      });\n      if (response.success) {\n        setAuthStatus(AuthStatus.SUCCESS);\n\n        // Delay navigation to show success message\n        setTimeout(() => {\n          // Use React Router navigation instead of hard redirect\n          navigate('/admin/dashboard');\n        }, 2000);\n      } else {\n        setLoginError(response.message || 'Two-factor authentication failed');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      const errorMessage = ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || err.message || 'Two-factor authentication failed';\n      setLoginError(errorMessage);\n      setAuthStatus(AuthStatus.ERROR);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      className: \"w-full max-w-md shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          className: \"text-primary\",\n          children: \"Admin Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"Secure login with HTTP-only cookies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), authStatus === AuthStatus.LOADING && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.LOADING,\n        title: \"Authenticating\",\n        message: \"Please wait while we verify your credentials...\",\n        showProgress: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this), authStatus === AuthStatus.SUCCESS && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.SUCCESS,\n        title: \"Login Successful\",\n        message: \"You have been successfully authenticated.\",\n        details: \"Redirecting to dashboard...\",\n        autoHideDuration: 2000\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this), authStatus === AuthStatus.ERROR && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.ERROR,\n        title: \"Login Error\",\n        message: loginError || error || \"Authentication failed\",\n        details: \"Please check your credentials and try again.\",\n        onClose: () => {\n          clearError();\n          setLoginError(null);\n          setAuthStatus(AuthStatus.IDLE);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this), authStatus === AuthStatus.INFO && showTwoFactor && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.INFO,\n        title: \"Two-Factor Authentication Required\",\n        message: \"Please enter the verification code from your authenticator app.\",\n        showProgress: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), !showTwoFactor ? /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        name: \"secure_login\",\n        initialValues: {\n          remember: true\n        },\n        onFinish: onFinish,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          rules: [{\n            required: true,\n            message: 'Please input your email!'\n          }, {\n            type: 'email',\n            message: 'Please enter a valid email address!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n              className: \"site-form-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 25\n            }, this),\n            placeholder: \"Email\",\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          rules: [{\n            required: true,\n            message: 'Please input your password!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n              className: \"site-form-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 25\n            }, this),\n            placeholder: \"Password\",\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            className: \"w-full\",\n            size: \"large\",\n            loading: loading,\n            children: \"Log in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/admin/forgot-password\",\n            className: \"text-primary hover:underline\",\n            children: \"Forgot password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"Two-Factor Authentication Required\",\n          description: \"Please enter the verification code from your authenticator app.\",\n          type: \"info\",\n          showIcon: true,\n          className: \"mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {\n              className: \"site-form-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 25\n            }, this),\n            placeholder: \"Verification Code\",\n            size: \"large\",\n            value: twoFactorToken,\n            onChange: e => setTwoFactorToken(e.target.value),\n            maxLength: 6\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          className: \"w-full\",\n          size: \"large\",\n          onClick: handleTwoFactorVerify,\n          loading: loading,\n          children: \"Verify\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          className: \"w-full mt-2\",\n          onClick: () => {\n            setShowTwoFactor(false);\n            setTwoFactorToken('');\n            setAdminId(null);\n          },\n          children: \"Back to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-sm text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This is a secure login that uses HTTP-only cookies for authentication.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Your session will be protected against CSRF attacks.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '20px'\n      },\n      children: /*#__PURE__*/_jsxDEV(AuthDebugger, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(SecureLogin, \"Day6WkRsvs6/w+KvXiOiJSdB1wU=\", false, function () {\n  return [useNavigate, useSecureAuth, Form.useForm];\n});\n_c = SecureLogin;\nexport default SecureLogin;\nvar _c;\n$RefreshReg$(_c, \"SecureLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "Form", "Input", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "Typography", "Divider", "UserOutlined", "LockOutlined", "SafetyOutlined", "useSecureAuth", "secureApiService", "AuthFeedback", "AuthStatus", "AuthDebugger", "jsxDEV", "_jsxDEV", "Title", "Text", "SecureLogin", "_s", "navigate", "adminLogin", "isAuthenticated", "isAdmin", "loading", "error", "clearError", "form", "useForm", "showTwoFactor", "setShowTwoFactor", "twoFactorToken", "setTwoFactorToken", "adminId", "setAdminId", "loginError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "authStatus", "setAuthStatus", "IDLE", "console", "log", "onFinish", "values", "LOADING", "email", "response", "fetch", "data", "json", "password", "SUCCESS", "err", "_err$response", "_err$response2", "_err$response2$data", "status", "requireTwoFactor", "INFO", "_err$response3", "_err$response3$data", "errorMessage", "message", "ERROR", "handleTwoFactorVerify", "post", "token", "success", "setTimeout", "_err$response4", "_err$response4$data", "className", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "title", "showProgress", "details", "autoHideDuration", "onClose", "name", "initialValues", "remember", "layout", "<PERSON><PERSON>", "rules", "required", "prefix", "placeholder", "size", "Password", "htmlType", "to", "description", "showIcon", "value", "onChange", "e", "target", "max<PERSON><PERSON><PERSON>", "onClick", "process", "env", "NODE_ENV", "style", "marginTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Form, Input, Button, Card, Alert, Typography, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';\nimport { ApiResponse } from '../../types/api';\nimport { useSecureAuth } from '../../contexts/SecureAuthContext';\nimport secureApiService from '../../services/secureApiService';\nimport AuthFeedback, { AuthStatus } from '../../components/AuthFeedback';\nimport AuthDebugger from '../../components/AuthDebugger';\n\nconst { Title, Text } = Typography;\n\nconst SecureLogin: React.FC = () => {\n  const navigate = useNavigate();\n  const { adminLogin, isAuthenticated, isAdmin, loading, error, clearError } = useSecureAuth();\n  const [form] = Form.useForm();\n  const [showTwoFactor, setShowTwoFactor] = useState(false);\n  const [twoFactorToken, setTwoFactorToken] = useState('');\n  const [adminId, setAdminId] = useState<number | null>(null);\n  const [loginError, setLoginError] = useState<string | null>(null);\n  const [authStatus, setAuthStatus] = useState<AuthStatus>(AuthStatus.IDLE);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    console.log('Authentication state changed:', { isAuthenticated, isAdmin, loading });\n    if (isAuthenticated && isAdmin && !loading) {\n      console.log('Redirecting to dashboard...');\n      // Use React Router navigation instead of hard redirect\n      navigate('/admin/dashboard');\n    }\n  }, [isAuthenticated, isAdmin, loading, navigate]);\n\n  // Clear errors when component unmounts\n  useEffect(() => {\n    return () => {\n      clearError();\n      setLoginError(null);\n    };\n  }, [clearError]);\n\n  // Handle form submission\n  const onFinish = async (values: { email: string; password: string }) => {\n    try {\n      setLoginError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      console.log('Attempting to login with:', values.email);\n\n      // Test direct API call first\n      try {\n        console.log('Testing direct API call to backend...');\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('API health check successful:', data);\n      } catch (error) {\n        console.error('API health check failed:', error);\n      }\n\n      // Attempt to login\n      await adminLogin(values.email, values.password);\n\n      // If we get here, login was successful\n      console.log('Login successful, setting success state');\n      setAuthStatus(AuthStatus.SUCCESS);\n\n      // Navigate without page reload - the useEffect will handle the redirect\n      console.log('Login successful, waiting for auth state update...');\n    } catch (err: any) {\n      console.error('Login error:', err);\n      // Check if 2FA is required\n      if (err.response?.status === 403 && err.response?.data?.requireTwoFactor) {\n        setShowTwoFactor(true);\n        setAdminId(err.response.data.adminId);\n        setAuthStatus(AuthStatus.INFO);\n      } else {\n        const errorMessage = err.response?.data?.message || err.message || 'Login failed';\n        console.error('Setting error state:', errorMessage);\n        setLoginError(errorMessage);\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    }\n  };\n\n  // Handle 2FA verification\n  const handleTwoFactorVerify = async () => {\n    try {\n      if (!adminId) {\n        setLoginError('Admin ID is missing');\n        setAuthStatus(AuthStatus.ERROR);\n        return;\n      }\n\n      setAuthStatus(AuthStatus.LOADING);\n\n      const response = await secureApiService.post<ApiResponse>('/2fa/verify', {\n        adminId,\n        token: twoFactorToken\n      });\n\n      if (response.success) {\n        setAuthStatus(AuthStatus.SUCCESS);\n\n        // Delay navigation to show success message\n        setTimeout(() => {\n          // Use React Router navigation instead of hard redirect\n          navigate('/admin/dashboard');\n        }, 2000);\n      } else {\n        setLoginError(response.message || 'Two-factor authentication failed');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err: any) {\n      const errorMessage = err.response?.data?.message || err.message || 'Two-factor authentication failed';\n      setLoginError(errorMessage);\n      setAuthStatus(AuthStatus.ERROR);\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-100\">\n      <Card className=\"w-full max-w-md shadow-lg\">\n        <div className=\"text-center mb-6\">\n          <Title level={2} className=\"text-primary\">\n            Admin Portal\n          </Title>\n          <Text type=\"secondary\">\n            Secure login with HTTP-only cookies\n          </Text>\n        </div>\n\n        {/* Authentication feedback */}\n        {authStatus === AuthStatus.LOADING && (\n          <AuthFeedback\n            status={AuthStatus.LOADING}\n            title=\"Authenticating\"\n            message=\"Please wait while we verify your credentials...\"\n            showProgress={false}\n          />\n        )}\n\n        {authStatus === AuthStatus.SUCCESS && (\n          <AuthFeedback\n            status={AuthStatus.SUCCESS}\n            title=\"Login Successful\"\n            message=\"You have been successfully authenticated.\"\n            details=\"Redirecting to dashboard...\"\n            autoHideDuration={2000}\n          />\n        )}\n\n        {authStatus === AuthStatus.ERROR && (\n          <AuthFeedback\n            status={AuthStatus.ERROR}\n            title=\"Login Error\"\n            message={loginError || error || \"Authentication failed\"}\n            details=\"Please check your credentials and try again.\"\n            onClose={() => {\n              clearError();\n              setLoginError(null);\n              setAuthStatus(AuthStatus.IDLE);\n            }}\n          />\n        )}\n\n        {authStatus === AuthStatus.INFO && showTwoFactor && (\n          <AuthFeedback\n            status={AuthStatus.INFO}\n            title=\"Two-Factor Authentication Required\"\n            message=\"Please enter the verification code from your authenticator app.\"\n            showProgress={false}\n          />\n        )}\n\n        {!showTwoFactor ? (\n          <Form\n            form={form}\n            name=\"secure_login\"\n            initialValues={{ remember: true }}\n            onFinish={onFinish}\n            layout=\"vertical\"\n          >\n            <Form.Item\n              name=\"email\"\n              rules={[\n                { required: true, message: 'Please input your email!' },\n                { type: 'email', message: 'Please enter a valid email address!' }\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined className=\"site-form-item-icon\" />}\n                placeholder=\"Email\"\n                size=\"large\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[{ required: true, message: 'Please input your password!' }]}\n            >\n              <Input.Password\n                prefix={<LockOutlined className=\"site-form-item-icon\" />}\n                placeholder=\"Password\"\n                size=\"large\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                className=\"w-full\"\n                size=\"large\"\n                loading={loading}\n              >\n                Log in\n              </Button>\n            </Form.Item>\n\n            <div className=\"text-center\">\n              <Link to=\"/admin/forgot-password\" className=\"text-primary hover:underline\">\n                Forgot password?\n              </Link>\n            </div>\n          </Form>\n        ) : (\n          <div>\n            <Alert\n              message=\"Two-Factor Authentication Required\"\n              description=\"Please enter the verification code from your authenticator app.\"\n              type=\"info\"\n              showIcon\n              className=\"mb-4\"\n            />\n\n            <div className=\"mb-4\">\n              <Input\n                prefix={<SafetyOutlined className=\"site-form-item-icon\" />}\n                placeholder=\"Verification Code\"\n                size=\"large\"\n                value={twoFactorToken}\n                onChange={(e) => setTwoFactorToken(e.target.value)}\n                maxLength={6}\n              />\n            </div>\n\n            <Button\n              type=\"primary\"\n              className=\"w-full\"\n              size=\"large\"\n              onClick={handleTwoFactorVerify}\n              loading={loading}\n            >\n              Verify\n            </Button>\n\n            <Button\n              type=\"link\"\n              className=\"w-full mt-2\"\n              onClick={() => {\n                setShowTwoFactor(false);\n                setTwoFactorToken('');\n                setAdminId(null);\n              }}\n            >\n              Back to Login\n            </Button>\n          </div>\n        )}\n\n        <Divider />\n\n        <div className=\"text-center text-sm text-gray-500\">\n          <p>This is a secure login that uses HTTP-only cookies for authentication.</p>\n          <p>Your session will be protected against CSRF attacks.</p>\n        </div>\n      </Card>\n\n      {/* Debug panel - only show in development */}\n      {process.env.NODE_ENV === 'development' && (\n        <div style={{ marginTop: '20px' }}>\n          <AuthDebugger />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SecureLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC5E,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAE9E,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,YAAY,IAAIC,UAAU,QAAQ,+BAA+B;AACxE,OAAOC,YAAY,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGb,UAAU;AAElC,MAAMc,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB,UAAU;IAAEC,eAAe;IAAEC,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGjB,aAAa,CAAC,CAAC;EAC5F,MAAM,CAACkB,IAAI,CAAC,GAAG5B,IAAI,CAAC6B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAgB,IAAI,CAAC;EACjE,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAaiB,UAAU,CAAC2B,IAAI,CAAC;;EAEzE;EACA3C,SAAS,CAAC,MAAM;IACd4C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAAEnB,eAAe;MAAEC,OAAO;MAAEC;IAAQ,CAAC,CAAC;IACnF,IAAIF,eAAe,IAAIC,OAAO,IAAI,CAACC,OAAO,EAAE;MAC1CgB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C;MACArB,QAAQ,CAAC,kBAAkB,CAAC;IAC9B;EACF,CAAC,EAAE,CAACE,eAAe,EAAEC,OAAO,EAAEC,OAAO,EAAEJ,QAAQ,CAAC,CAAC;;EAEjD;EACAxB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX8B,UAAU,CAAC,CAAC;MACZU,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMgB,QAAQ,GAAG,MAAOC,MAA2C,IAAK;IACtE,IAAI;MACFP,aAAa,CAAC,IAAI,CAAC;MACnBE,aAAa,CAAC1B,UAAU,CAACgC,OAAO,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,MAAM,CAACE,KAAK,CAAC;;MAEtD;MACA,IAAI;QACFL,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEO,IAAI,CAAC;MACnD,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;;MAEA;MACA,MAAMJ,UAAU,CAACsB,MAAM,CAACE,KAAK,EAAEF,MAAM,CAACO,QAAQ,CAAC;;MAE/C;MACAV,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDH,aAAa,CAAC1B,UAAU,CAACuC,OAAO,CAAC;;MAEjC;MACAX,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACnE,CAAC,CAAC,OAAOW,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,mBAAA;MACjBf,OAAO,CAACf,KAAK,CAAC,cAAc,EAAE2B,GAAG,CAAC;MAClC;MACA,IAAI,EAAAC,aAAA,GAAAD,GAAG,CAACN,QAAQ,cAAAO,aAAA,uBAAZA,aAAA,CAAcG,MAAM,MAAK,GAAG,KAAAF,cAAA,GAAIF,GAAG,CAACN,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcN,IAAI,cAAAO,mBAAA,eAAlBA,mBAAA,CAAoBE,gBAAgB,EAAE;QACxE3B,gBAAgB,CAAC,IAAI,CAAC;QACtBI,UAAU,CAACkB,GAAG,CAACN,QAAQ,CAACE,IAAI,CAACf,OAAO,CAAC;QACrCK,aAAa,CAAC1B,UAAU,CAAC8C,IAAI,CAAC;MAChC,CAAC,MAAM;QAAA,IAAAC,cAAA,EAAAC,mBAAA;QACL,MAAMC,YAAY,GAAG,EAAAF,cAAA,GAAAP,GAAG,CAACN,QAAQ,cAAAa,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,uBAAlBA,mBAAA,CAAoBE,OAAO,KAAIV,GAAG,CAACU,OAAO,IAAI,cAAc;QACjFtB,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEoC,YAAY,CAAC;QACnDzB,aAAa,CAACyB,YAAY,CAAC;QAC3BvB,aAAa,CAAC1B,UAAU,CAACmD,KAAK,CAAC;MACjC;IACF;EACF,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,IAAI,CAAC/B,OAAO,EAAE;QACZG,aAAa,CAAC,qBAAqB,CAAC;QACpCE,aAAa,CAAC1B,UAAU,CAACmD,KAAK,CAAC;QAC/B;MACF;MAEAzB,aAAa,CAAC1B,UAAU,CAACgC,OAAO,CAAC;MAEjC,MAAME,QAAQ,GAAG,MAAMpC,gBAAgB,CAACuD,IAAI,CAAc,aAAa,EAAE;QACvEhC,OAAO;QACPiC,KAAK,EAAEnC;MACT,CAAC,CAAC;MAEF,IAAIe,QAAQ,CAACqB,OAAO,EAAE;QACpB7B,aAAa,CAAC1B,UAAU,CAACuC,OAAO,CAAC;;QAEjC;QACAiB,UAAU,CAAC,MAAM;UACf;UACAhD,QAAQ,CAAC,kBAAkB,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLgB,aAAa,CAACU,QAAQ,CAACgB,OAAO,IAAI,kCAAkC,CAAC;QACrExB,aAAa,CAAC1B,UAAU,CAACmD,KAAK,CAAC;MACjC;IACF,CAAC,CAAC,OAAOX,GAAQ,EAAE;MAAA,IAAAiB,cAAA,EAAAC,mBAAA;MACjB,MAAMT,YAAY,GAAG,EAAAQ,cAAA,GAAAjB,GAAG,CAACN,QAAQ,cAAAuB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,uBAAlBA,mBAAA,CAAoBR,OAAO,KAAIV,GAAG,CAACU,OAAO,IAAI,kCAAkC;MACrG1B,aAAa,CAACyB,YAAY,CAAC;MAC3BvB,aAAa,CAAC1B,UAAU,CAACmD,KAAK,CAAC;IACjC;EACF,CAAC;EAED,oBACEhD,OAAA;IAAKwD,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBACxEzD,OAAA,CAACb,IAAI;MAACqE,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACzCzD,OAAA;QAAKwD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzD,OAAA,CAACC,KAAK;UAACyD,KAAK,EAAE,CAAE;UAACF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE1C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR9D,OAAA,CAACE,IAAI;UAAC6D,IAAI,EAAC,WAAW;UAAAN,QAAA,EAAC;QAEvB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGLxC,UAAU,KAAKzB,UAAU,CAACgC,OAAO,iBAChC7B,OAAA,CAACJ,YAAY;QACX6C,MAAM,EAAE5C,UAAU,CAACgC,OAAQ;QAC3BmC,KAAK,EAAC,gBAAgB;QACtBjB,OAAO,EAAC,iDAAiD;QACzDkB,YAAY,EAAE;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACF,EAEAxC,UAAU,KAAKzB,UAAU,CAACuC,OAAO,iBAChCpC,OAAA,CAACJ,YAAY;QACX6C,MAAM,EAAE5C,UAAU,CAACuC,OAAQ;QAC3B4B,KAAK,EAAC,kBAAkB;QACxBjB,OAAO,EAAC,2CAA2C;QACnDmB,OAAO,EAAC,6BAA6B;QACrCC,gBAAgB,EAAE;MAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACF,EAEAxC,UAAU,KAAKzB,UAAU,CAACmD,KAAK,iBAC9BhD,OAAA,CAACJ,YAAY;QACX6C,MAAM,EAAE5C,UAAU,CAACmD,KAAM;QACzBgB,KAAK,EAAC,aAAa;QACnBjB,OAAO,EAAE3B,UAAU,IAAIV,KAAK,IAAI,uBAAwB;QACxDwD,OAAO,EAAC,8CAA8C;QACtDE,OAAO,EAAEA,CAAA,KAAM;UACbzD,UAAU,CAAC,CAAC;UACZU,aAAa,CAAC,IAAI,CAAC;UACnBE,aAAa,CAAC1B,UAAU,CAAC2B,IAAI,CAAC;QAChC;MAAE;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EAEAxC,UAAU,KAAKzB,UAAU,CAAC8C,IAAI,IAAI7B,aAAa,iBAC9Cd,OAAA,CAACJ,YAAY;QACX6C,MAAM,EAAE5C,UAAU,CAAC8C,IAAK;QACxBqB,KAAK,EAAC,oCAAoC;QAC1CjB,OAAO,EAAC,iEAAiE;QACzEkB,YAAY,EAAE;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACF,EAEA,CAAChD,aAAa,gBACbd,OAAA,CAAChB,IAAI;QACH4B,IAAI,EAAEA,IAAK;QACXyD,IAAI,EAAC,cAAc;QACnBC,aAAa,EAAE;UAAEC,QAAQ,EAAE;QAAK,CAAE;QAClC5C,QAAQ,EAAEA,QAAS;QACnB6C,MAAM,EAAC,UAAU;QAAAf,QAAA,gBAEjBzD,OAAA,CAAChB,IAAI,CAACyF,IAAI;UACRJ,IAAI,EAAC,OAAO;UACZK,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE5B,OAAO,EAAE;UAA2B,CAAC,EACvD;YAAEgB,IAAI,EAAE,OAAO;YAAEhB,OAAO,EAAE;UAAsC,CAAC,CACjE;UAAAU,QAAA,eAEFzD,OAAA,CAACf,KAAK;YACJ2F,MAAM,eAAE5E,OAAA,CAACT,YAAY;cAACiE,SAAS,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzDe,WAAW,EAAC,OAAO;YACnBC,IAAI,EAAC;UAAO;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ9D,OAAA,CAAChB,IAAI,CAACyF,IAAI;UACRJ,IAAI,EAAC,UAAU;UACfK,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5B,OAAO,EAAE;UAA8B,CAAC,CAAE;UAAAU,QAAA,eAEpEzD,OAAA,CAACf,KAAK,CAAC8F,QAAQ;YACbH,MAAM,eAAE5E,OAAA,CAACR,YAAY;cAACgE,SAAS,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzDe,WAAW,EAAC,UAAU;YACtBC,IAAI,EAAC;UAAO;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ9D,OAAA,CAAChB,IAAI,CAACyF,IAAI;UAAAhB,QAAA,eACRzD,OAAA,CAACd,MAAM;YACL6E,IAAI,EAAC,SAAS;YACdiB,QAAQ,EAAC,QAAQ;YACjBxB,SAAS,EAAC,QAAQ;YAClBsB,IAAI,EAAC,OAAO;YACZrE,OAAO,EAAEA,OAAQ;YAAAgD,QAAA,EAClB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ9D,OAAA;UAAKwD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BzD,OAAA,CAAClB,IAAI;YAACmG,EAAE,EAAC,wBAAwB;YAACzB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAE3E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAEP9D,OAAA;QAAAyD,QAAA,gBACEzD,OAAA,CAACZ,KAAK;UACJ2D,OAAO,EAAC,oCAAoC;UAC5CmC,WAAW,EAAC,iEAAiE;UAC7EnB,IAAI,EAAC,MAAM;UACXoB,QAAQ;UACR3B,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAEF9D,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBzD,OAAA,CAACf,KAAK;YACJ2F,MAAM,eAAE5E,OAAA,CAACP,cAAc;cAAC+D,SAAS,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3De,WAAW,EAAC,mBAAmB;YAC/BC,IAAI,EAAC,OAAO;YACZM,KAAK,EAAEpE,cAAe;YACtBqE,QAAQ,EAAGC,CAAC,IAAKrE,iBAAiB,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDI,SAAS,EAAE;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9D,OAAA,CAACd,MAAM;UACL6E,IAAI,EAAC,SAAS;UACdP,SAAS,EAAC,QAAQ;UAClBsB,IAAI,EAAC,OAAO;UACZW,OAAO,EAAExC,qBAAsB;UAC/BxC,OAAO,EAAEA,OAAQ;UAAAgD,QAAA,EAClB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9D,OAAA,CAACd,MAAM;UACL6E,IAAI,EAAC,MAAM;UACXP,SAAS,EAAC,aAAa;UACvBiC,OAAO,EAAEA,CAAA,KAAM;YACb1E,gBAAgB,CAAC,KAAK,CAAC;YACvBE,iBAAiB,CAAC,EAAE,CAAC;YACrBE,UAAU,CAAC,IAAI,CAAC;UAClB,CAAE;UAAAsC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAED9D,OAAA,CAACV,OAAO;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEX9D,OAAA;QAAKwD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDzD,OAAA;UAAAyD,QAAA,EAAG;QAAsE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7E9D,OAAA;UAAAyD,QAAA,EAAG;QAAoD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGN4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC5F,OAAA;MAAK6F,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAArC,QAAA,eAChCzD,OAAA,CAACF,YAAY;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAhRID,WAAqB;EAAA,QACRpB,WAAW,EACiDW,aAAa,EAC3EV,IAAI,CAAC6B,OAAO;AAAA;AAAAkF,EAAA,GAHvB5F,WAAqB;AAkR3B,eAAeA,WAAW;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}