{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { Helmet } from 'react-helmet';\nimport { Al<PERSON>, Tag, Button, Badge } from 'antd';\nimport { extractIdFromSlug } from '../utils/slugify';\nimport dateUtils, { DateFormat } from '../utils/dateUtils';\nimport { CalendarOutlined, GlobalOutlined, BankOutlined, BookOutlined, CheckCircleOutlined, DollarOutlined, FileTextOutlined, LinkOutlined, TrophyOutlined, UserOutlined, YoutubeOutlined, ClockCircleOutlined } from '@ant-design/icons';\n\n// Define the Scholarship interface based on the Prisma schema\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Loading skeleton component\nconst LoadingSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-pulse\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-96 bg-gray-200 rounded-xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:col-span-2 space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-8 bg-gray-200 rounded w-1/3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-gray-200 rounded w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-gray-200 rounded w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-gray-200 rounded w-5/6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-8 bg-gray-200 rounded w-1/3 mt-8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-gray-200 rounded w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-gray-200 rounded w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 59,\n  columnNumber: 3\n}, this);\n_c = LoadingSkeleton;\nconst EnhancedScholarshipDetailPage = () => {\n  _s();\n  const {\n    id,\n    slug\n  } = useParams();\n  const navigate = useNavigate();\n  const [scholarship, setScholarship] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchScholarship = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        let scholarshipId = null;\n\n        // If we have an ID, use it directly\n        if (id) {\n          scholarshipId = id;\n        }\n        // If we have a slug, extract the ID from it\n        else if (slug) {\n          const extractedId = extractIdFromSlug(slug);\n          if (extractedId) {\n            scholarshipId = extractedId.toString();\n          } else {\n            throw new Error('Invalid slug format');\n          }\n        }\n        if (scholarshipId) {\n          // Log the request for debugging\n          console.log(`Fetching scholarship with ID ${scholarshipId}`);\n          try {\n            // Log the URL we're fetching from\n            console.log(`Fetching from URL: /api/scholarships/${scholarshipId}`);\n\n            // Make the API request using axios directly to avoid any interceptors\n            const response = await fetch(`http://localhost:5000/api/scholarships/${scholarshipId}`);\n            if (!response.ok) {\n              throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            console.log('API Response:', data);\n\n            // Handle the correct API response format: { success: true, data: {...} }\n            const scholarshipData = data.data || data;\n\n            // Process the scholarship data\n            if (scholarshipData) {\n              processScholarshipData(scholarshipData);\n            } else {\n              console.error('Unexpected API response format:', data);\n              throw new Error('Invalid API response format');\n            }\n          } catch (apiError) {\n            // This will be caught by the outer try/catch block\n            throw apiError;\n          }\n        } else {\n          throw new Error('No valid ID or slug provided');\n        }\n      } catch (err) {\n        console.error('Error fetching scholarship:', err);\n\n        // Provide more detailed error message if available\n        if (err.response) {\n          var _err$response$data;\n          // The request was made and the server responded with a status code\n          // that falls out of the range of 2xx\n          const statusCode = err.response.status;\n          const errorMessage = ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'An error occurred';\n          if (statusCode === 404) {\n            setError(`La bourse avec l'ID ${id || slug} n'existe pas. Veuillez vérifier l'ID et réessayer.`);\n          } else {\n            setError(`Erreur ${statusCode}: ${errorMessage}`);\n          }\n        } else if (err.request) {\n          // The request was made but no response was received\n          setError('Aucune réponse reçue du serveur. Veuillez vérifier votre connexion et que le serveur backend est en cours d\\'exécution.');\n          console.log('Request that failed:', err.request);\n        } else {\n          // Something happened in setting up the request that triggered an Error\n          setError(`Échec du chargement des détails de la bourse: ${err.message}`);\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchScholarship();\n  }, [id, slug]);\n\n  // Process and enhance the scholarship data\n  const processScholarshipData = data => {\n    // Ensure we have valid date strings\n    const deadline = typeof data.deadline === 'object' ? new Date(data.deadline).toISOString() : data.deadline;\n    const createdAt = data.createdAt || new Date().toISOString();\n    const updatedAt = data.updatedAt || new Date().toISOString();\n\n    // Calculate date-related fields if not provided by the API\n    const isExpired = data.isExpired !== undefined ? data.isExpired : dateUtils.isDatePast(deadline);\n    const daysRemaining = data.daysRemaining !== undefined ? data.daysRemaining : dateUtils.getDaysRemaining(deadline);\n    const formattedDeadline = data.formattedDeadline || dateUtils.formatDate(deadline, DateFormat.MEDIUM);\n\n    // Parse string fields into arrays where needed\n    const enhancedData = {\n      ...data,\n      deadline,\n      createdAt,\n      updatedAt,\n      isExpired,\n      daysRemaining,\n      formattedDeadline,\n      // Parse financial benefits into a list if it exists\n      financial_benefits_list: data.financial_benefits_summary ? data.financial_benefits_summary.split(',').map(item => item.trim()) : [],\n      // Parse eligibility criteria into a list if it exists\n      eligibility_criteria_list: data.eligibility_summary ? data.eligibility_summary.split(',').map(item => item.trim()) : [],\n      // These fields might not exist in the database, so we create placeholders\n      // In a real implementation, these would come from the database or be parsed from other fields\n      study_fields: data.study_fields || ['Business', 'Engineering', 'Computer Science', 'Medicine'],\n      universities: data.universities || ['University of Paris', 'Sorbonne University', 'École Polytechnique'],\n      required_documents: data.required_documents || [{\n        name: 'CV/Resume',\n        description: 'Updated curriculum vitae'\n      }, {\n        name: 'Motivation Letter',\n        description: 'Explaining why you deserve this scholarship'\n      }, {\n        name: 'Academic Transcripts',\n        description: 'Official transcripts from your institution'\n      }],\n      deadline_description: data.deadline_description || 'Applications must be submitted before midnight on the deadline date.'\n    };\n\n    // Log the processed data for debugging\n    console.log('Processed scholarship data:', enhancedData);\n    setScholarship(enhancedData);\n  };\n\n  // Render loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSkeleton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Render error state\n  if (error || !scholarship) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Bourse non trouv\\xE9e\",\n        description: error || \"La bourse que vous recherchez n'existe pas ou a été supprimée.\",\n        type: \"error\",\n        showIcon: true,\n        className: \"mb-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/scholarships\",\n          className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n          children: \"Retour aux bourses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: [scholarship.title, \" | MaBourse\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: `${scholarship.title} - ${scholarship.description.substring(0, 160)}...`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:title\",\n        content: `${scholarship.title} | MaBourse`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:description\",\n        content: `${scholarship.title} - ${scholarship.description.substring(0, 160)}...`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), scholarship.thumbnail && /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:image\",\n        content: scholarship.thumbnail\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 35\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        type: \"application/ld+json\",\n        children: JSON.stringify({\n          '@context': 'https://schema.org',\n          '@type': 'EducationalOccupationalCredential',\n          'name': scholarship.title,\n          'description': scholarship.description,\n          'educationalLevel': scholarship.level,\n          'validIn': {\n            '@type': 'Country',\n            'name': scholarship.country\n          },\n          'validUntil': scholarship.deadline\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative rounded-xl overflow-hidden mb-6 shadow-md h-24 bg-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative h-full flex items-center px-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-4xl\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl sm:text-3xl font-bold text-gray-800\",\n              children: scholarship.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2 space-y-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-4 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {\n                className: \"text-primary mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), \"Informations cl\\xE9s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-sm space-y-1.5\",\n              children: [scholarship.deadline && /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {\n                  className: \"text-primary mr-2 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Date limite :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600 ml-1\",\n                      children: scholarship.formattedDeadline || dateUtils.formatDate(scholarship.deadline, DateFormat.MEDIUM)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 25\n                    }, this), scholarship.isExpired ? /*#__PURE__*/_jsxDEV(Badge, {\n                      className: \"ml-2\",\n                      count: \"Expir\\xE9e\",\n                      style: {\n                        backgroundColor: '#ff4d4f'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Badge, {\n                      className: \"ml-2\",\n                      count: `${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} restant${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''}`,\n                      style: {\n                        backgroundColor: (scholarship.daysRemaining || 0) <= 7 ? '#faad14' : '#52c41a'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 23\n                  }, this), !scholarship.isExpired && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 mt-1 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n                      className: \"mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 27\n                    }, this), (scholarship.daysRemaining || 0) === 0 ? \"Dernier jour pour postuler !\" : `Il reste ${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} pour postuler`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), scholarship.level && /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(UserOutlined, {\n                  className: \"text-primary mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Niveau d'\\xE9tudes :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600 ml-1\",\n                  children: scholarship.level\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), scholarship.country && /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(GlobalOutlined, {\n                  className: \"text-primary mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Pays :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600 ml-1\",\n                  children: scholarship.country\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), scholarship.coverage && /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {\n                  className: \"text-primary mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Couverture :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600 ml-1\",\n                  children: scholarship.coverage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"prose max-w-none text-gray-600 text-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: scholarship.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), scholarship.eligibility_summary && /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                className: \"text-primary mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), \"Crit\\xE8res d'\\xC9ligibilit\\xE9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mb-2\",\n                children: scholarship.eligibility_summary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), scholarship.eligibility_criteria_list && scholarship.eligibility_criteria_list.length > 0 && /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-disc pl-5 space-y-1\",\n                children: scholarship.eligibility_criteria_list.map((criterion, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"text-gray-600\",\n                  children: criterion\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), scholarship.financial_benefits_summary && /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(DollarOutlined, {\n                className: \"text-primary mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), \"Avantages Financiers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mb-2\",\n                children: scholarship.financial_benefits_summary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this), scholarship.financial_benefits_list && scholarship.financial_benefits_list.length > 0 && /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-disc pl-5 space-y-1\",\n                children: scholarship.financial_benefits_list.map((benefit, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"text-gray-600\",\n                  children: benefit\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), scholarship.study_fields && scholarship.study_fields.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(BookOutlined, {\n                className: \"text-primary mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), \"Domaines d'\\xC9tudes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2\",\n              children: scholarship.study_fields.map((field, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                className: \"px-2 py-0.5 text-xs rounded-full\",\n                children: field\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this), scholarship.universities && scholarship.universities.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(BankOutlined, {\n                className: \"text-primary mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this), \"Universit\\xE9s Participantes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n              children: scholarship.universities.map((university, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center p-2 bg-gray-50 rounded-lg text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(BankOutlined, {\n                  className: \"text-gray-400 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-700\",\n                  children: university\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this), scholarship.required_documents && scholarship.required_documents.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {\n                className: \"text-primary mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), \"Documents Requis\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: scholarship.required_documents.map((doc, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-3 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-900 mb-1 text-sm\",\n                  children: doc.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-xs\",\n                  children: doc.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(LinkOutlined, {\n                className: \"text-primary mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), \"Liens Essentiels\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: [\"Site officiel de la bourse: \", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: scholarship.scholarship_link || \"https://example.com/scholarship\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-primary hover:text-primary-dark underline\",\n                  children: scholarship.scholarship_link || \"https://example.com/scholarship\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: [\"Portail de candidature en ligne: \", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://example.com/apply\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-primary hover:text-primary-dark underline\",\n                  children: \"https://example.com/apply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: [\"Guide d'application PDF: \", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://example.com/guide.pdf\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-primary hover:text-primary-dark underline\",\n                  children: \"https://example.com/guide.pdf\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: [\"FAQ sur la bourse: \", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://example.com/faq\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-primary hover:text-primary-dark underline\",\n                  children: \"https://example.com/faq\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white rounded-xl p-5 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(YoutubeOutlined, {\n                className: \"text-red-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this), \"Notre Cha\\xEEne YouTube\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: [\"Tutoriel vid\\xE9o sur cette bourse: \", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: scholarship.youtube_link || \"https://www.youtube.com/watch?v=example1\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-primary hover:text-primary-dark underline\",\n                  children: scholarship.youtube_link || \"https://www.youtube.com/watch?v=example1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: [\"Comment pr\\xE9parer votre dossier de candidature: \", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://www.youtube.com/watch?v=example2\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-primary hover:text-primary-dark underline\",\n                  children: \"https://www.youtube.com/watch?v=example2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: [\"Visitez notre cha\\xEEne YouTube pour plus de tutoriels: \", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://www.youtube.com/channel/UCxxxxxxxxxxx\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-primary hover:text-primary-dark underline\",\n                  children: \"https://www.youtube.com/channel/UCxxxxxxxxxxx\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-4 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-base font-bold text-gray-900 mb-2\",\n              children: \"Restez inform\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 mb-3\",\n              children: \"Recevez les derni\\xE8res bourses et opportunit\\xE9s directement dans votre bo\\xEEte mail.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  placeholder: \"Votre adresse email\",\n                  className: \"w-full px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                size: \"small\",\n                className: \"w-full\",\n                children: \"S'abonner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"En vous inscrivant, vous acceptez notre politique de confidentialit\\xE9.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-4 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-base font-bold text-gray-900 mb-3\",\n              children: \"Bourses Similaires\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [[6, 7, 8, 10, 11, 13].map((scholarshipId, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group flex items-start p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                onClick: () => navigate(`/scholarships/${scholarshipId}`),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: `/assets/scholarship${index % 3 + 1}.jpg`,\n                    alt: \"Scholarship thumbnail\",\n                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform\",\n                    onError: e => {\n                      const target = e.target;\n                      target.src = '/assets/default-thumbnail.jpg';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-xs font-medium text-gray-900 group-hover:text-primary transition-colors line-clamp-2\",\n                    children: index === 0 ? \"Fulbright Scholarship Program\" : index === 1 ? \"Chevening Scholarship\" : index === 2 ? \"DAAD Scholarship\" : index === 3 ? \"Bourse du gouvernment indien\" : index === 4 ? \"Updated Test Scholarship\" : \"Example Scholarship 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mt-0.5\",\n                    children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {\n                      className: \"text-gray-400 text-xs mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500 truncate\",\n                      children: new Date(new Date().setMonth(new Date().getMonth() + index + 1)).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(GlobalOutlined, {\n                      className: \"text-gray-400 text-xs ml-2 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500 truncate\",\n                      children: index === 0 ? \"United States\" : index === 1 ? \"United Kingdom\" : index === 2 ? \"Germany\" : index === 3 ? \"Inde\" : index === 4 ? \"France\" : \"Canada\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-1 flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"default\",\n                  size: \"small\",\n                  className: \"flex-1\",\n                  onClick: () => navigate('/scholarships'),\n                  children: \"Voir plus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  size: \"small\",\n                  className: \"flex-1\",\n                  onClick: () => navigate('/scholarships'),\n                  children: \"Retour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(EnhancedScholarshipDetailPage, \"rKgEdYKKzz+WH8ztJ9M/XVIkZ0E=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c2 = EnhancedScholarshipDetailPage;\nexport default EnhancedScholarshipDetailPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"LoadingSkeleton\");\n$RefreshReg$(_c2, \"EnhancedScholarshipDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Link", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tag", "<PERSON><PERSON>", "Badge", "extractIdFromSlug", "dateUtils", "DateFormat", "CalendarOutlined", "GlobalOutlined", "BankOutlined", "BookOutlined", "CheckCircleOutlined", "DollarOutlined", "FileTextOutlined", "LinkOutlined", "TrophyOutlined", "UserOutlined", "YoutubeOutlined", "ClockCircleOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LoadingSkeleton", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "EnhancedScholarshipDetailPage", "_s", "id", "slug", "navigate", "scholarship", "setScholarship", "loading", "setLoading", "error", "setError", "fetchScholarship", "scholarshipId", "extractedId", "toString", "Error", "console", "log", "response", "fetch", "ok", "status", "data", "json", "scholarshipData", "processScholarshipData", "apiError", "err", "_err$response$data", "statusCode", "errorMessage", "message", "request", "deadline", "Date", "toISOString", "createdAt", "updatedAt", "isExpired", "undefined", "isDatePast", "daysRemaining", "getDaysRemaining", "formattedDeadline", "formatDate", "MEDIUM", "enhancedData", "financial_benefits_list", "financial_benefits_summary", "split", "map", "item", "trim", "eligibility_criteria_list", "eligibility_summary", "study_fields", "universities", "required_documents", "name", "description", "deadline_description", "type", "showIcon", "to", "title", "content", "substring", "property", "thumbnail", "JSON", "stringify", "level", "country", "count", "style", "backgroundColor", "coverage", "length", "criterion", "index", "benefit", "field", "color", "university", "doc", "href", "scholarship_link", "target", "rel", "youtube_link", "placeholder", "required", "size", "onClick", "src", "alt", "onError", "e", "setMonth", "getMonth", "toLocaleDateString", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, useNavigate, <PERSON> } from 'react-router-dom';\nimport { Helmet } from 'react-helmet';\nimport { <PERSON><PERSON>, Tag, <PERSON>ton, Spin, Badge } from 'antd';\nimport { extractIdFromSlug } from '../utils/slugify';\nimport dateUtils, { DateFormat } from '../utils/dateUtils';\nimport { getEnv } from '../utils/envValidator';\nimport {\n  CalendarOutlined,\n  GlobalOutlined,\n  BankOutlined,\n  BookOutlined,\n  CheckCircleOutlined,\n  DollarOutlined,\n  FileTextOutlined,\n  LinkOutlined,\n  TrophyOutlined,\n  UserOutlined,\n  YoutubeOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\n\n\n\n// Define the Scholarship interface based on the Prisma schema\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  level?: string;\n  country?: string;\n  deadline: string;\n  isOpen: boolean;\n  thumbnail?: string;\n  coverage?: string;\n  financial_benefits_summary?: string;\n  eligibility_summary?: string;\n  scholarship_link?: string;\n  youtube_link?: string;\n  createdAt: string;\n  updatedAt: string;\n\n  // Additional fields from the backend\n  isExpired?: boolean;\n  daysRemaining?: number;\n  formattedDeadline?: string;\n\n  // Additional fields that might not be in the database but we'll parse from existing fields\n  financial_benefits_list?: string[];\n  eligibility_criteria_list?: string[];\n  study_fields?: string[];\n  universities?: string[];\n  required_documents?: { name: string; description: string }[];\n  deadline_description?: string;\n}\n\n// Loading skeleton component\nconst LoadingSkeleton: React.FC = () => (\n  <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-pulse\">\n    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n      <div className=\"h-96 bg-gray-200 rounded-xl\"></div>\n      <div className=\"lg:col-span-2 space-y-6\">\n        <div className=\"h-8 bg-gray-200 rounded w-1/3\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n\n        <div className=\"h-8 bg-gray-200 rounded w-1/3 mt-8\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n      </div>\n    </div>\n  </div>\n);\n\nconst EnhancedScholarshipDetailPage: React.FC = () => {\n  const { id, slug } = useParams<{ id?: string; slug?: string }>();\n  const navigate = useNavigate();\n  const [scholarship, setScholarship] = useState<Scholarship | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchScholarship = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        let scholarshipId: string | null = null;\n\n        // If we have an ID, use it directly\n        if (id) {\n          scholarshipId = id;\n        }\n        // If we have a slug, extract the ID from it\n        else if (slug) {\n          const extractedId = extractIdFromSlug(slug);\n          if (extractedId) {\n            scholarshipId = extractedId.toString();\n          } else {\n            throw new Error('Invalid slug format');\n          }\n        }\n\n        if (scholarshipId) {\n          // Log the request for debugging\n          console.log(`Fetching scholarship with ID ${scholarshipId}`);\n\n          try {\n            // Log the URL we're fetching from\n            console.log(`Fetching from URL: /api/scholarships/${scholarshipId}`);\n\n            // Make the API request using axios directly to avoid any interceptors\n            const response = await fetch(`http://localhost:5000/api/scholarships/${scholarshipId}`);\n\n            if (!response.ok) {\n              throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n            console.log('API Response:', data);\n\n            // Handle the correct API response format: { success: true, data: {...} }\n            const scholarshipData = data.data || data;\n\n            // Process the scholarship data\n            if (scholarshipData) {\n              processScholarshipData(scholarshipData);\n            } else {\n              console.error('Unexpected API response format:', data);\n              throw new Error('Invalid API response format');\n            }\n          } catch (apiError) {\n            // This will be caught by the outer try/catch block\n            throw apiError;\n          }\n        } else {\n          throw new Error('No valid ID or slug provided');\n        }\n      } catch (err: any) {\n        console.error('Error fetching scholarship:', err);\n\n        // Provide more detailed error message if available\n        if (err.response) {\n          // The request was made and the server responded with a status code\n          // that falls out of the range of 2xx\n          const statusCode = err.response.status;\n          const errorMessage = err.response.data?.message || 'An error occurred';\n\n          if (statusCode === 404) {\n            setError(`La bourse avec l'ID ${id || slug} n'existe pas. Veuillez vérifier l'ID et réessayer.`);\n          } else {\n            setError(`Erreur ${statusCode}: ${errorMessage}`);\n          }\n        } else if (err.request) {\n          // The request was made but no response was received\n          setError('Aucune réponse reçue du serveur. Veuillez vérifier votre connexion et que le serveur backend est en cours d\\'exécution.');\n          console.log('Request that failed:', err.request);\n        } else {\n          // Something happened in setting up the request that triggered an Error\n          setError(`Échec du chargement des détails de la bourse: ${err.message}`);\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchScholarship();\n  }, [id, slug]);\n\n  // Process and enhance the scholarship data\n  const processScholarshipData = (data: Scholarship) => {\n    // Ensure we have valid date strings\n    const deadline = typeof data.deadline === 'object'\n      ? new Date(data.deadline).toISOString()\n      : data.deadline;\n\n    const createdAt = data.createdAt || new Date().toISOString();\n    const updatedAt = data.updatedAt || new Date().toISOString();\n\n    // Calculate date-related fields if not provided by the API\n    const isExpired = data.isExpired !== undefined\n      ? data.isExpired\n      : dateUtils.isDatePast(deadline);\n\n    const daysRemaining = data.daysRemaining !== undefined\n      ? data.daysRemaining\n      : dateUtils.getDaysRemaining(deadline);\n\n    const formattedDeadline = data.formattedDeadline || dateUtils.formatDate(deadline, DateFormat.MEDIUM);\n\n    // Parse string fields into arrays where needed\n    const enhancedData = {\n      ...data,\n      deadline,\n      createdAt,\n      updatedAt,\n      isExpired,\n      daysRemaining,\n      formattedDeadline,\n\n      // Parse financial benefits into a list if it exists\n      financial_benefits_list: data.financial_benefits_summary\n        ? data.financial_benefits_summary.split(',').map(item => item.trim())\n        : [],\n\n      // Parse eligibility criteria into a list if it exists\n      eligibility_criteria_list: data.eligibility_summary\n        ? data.eligibility_summary.split(',').map(item => item.trim())\n        : [],\n\n      // These fields might not exist in the database, so we create placeholders\n      // In a real implementation, these would come from the database or be parsed from other fields\n      study_fields: data.study_fields || ['Business', 'Engineering', 'Computer Science', 'Medicine'],\n      universities: data.universities || ['University of Paris', 'Sorbonne University', 'École Polytechnique'],\n      required_documents: data.required_documents || [\n        { name: 'CV/Resume', description: 'Updated curriculum vitae' },\n        { name: 'Motivation Letter', description: 'Explaining why you deserve this scholarship' },\n        { name: 'Academic Transcripts', description: 'Official transcripts from your institution' }\n      ],\n      deadline_description: data.deadline_description || 'Applications must be submitted before midnight on the deadline date.'\n    };\n\n    // Log the processed data for debugging\n    console.log('Processed scholarship data:', enhancedData);\n\n    setScholarship(enhancedData);\n  };\n\n  // Render loading state\n  if (loading) {\n    return <LoadingSkeleton />;\n  }\n\n  // Render error state\n  if (error || !scholarship) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <Alert\n          message=\"Bourse non trouvée\"\n          description={error || \"La bourse que vous recherchez n'existe pas ou a été supprimée.\"}\n          type=\"error\"\n          showIcon\n          className=\"mb-6\"\n        />\n        <div className=\"mt-6\">\n          <Link\n            to=\"/scholarships\"\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n          >\n            Retour aux bourses\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* SEO Metadata */}\n      <Helmet>\n        <title>{scholarship.title} | MaBourse</title>\n        <meta name=\"description\" content={`${scholarship.title} - ${scholarship.description.substring(0, 160)}...`} />\n        <meta property=\"og:title\" content={`${scholarship.title} | MaBourse`} />\n        <meta property=\"og:description\" content={`${scholarship.title} - ${scholarship.description.substring(0, 160)}...`} />\n        {scholarship.thumbnail && <meta property=\"og:image\" content={scholarship.thumbnail} />}\n        <script type=\"application/ld+json\">\n          {JSON.stringify({\n            '@context': 'https://schema.org',\n            '@type': 'EducationalOccupationalCredential',\n            'name': scholarship.title,\n            'description': scholarship.description,\n            'educationalLevel': scholarship.level,\n            'validIn': {\n              '@type': 'Country',\n              'name': scholarship.country\n            },\n            'validUntil': scholarship.deadline\n          })}\n        </script>\n      </Helmet>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Very Slim Hero Section - Only Title */}\n        <div className=\"relative rounded-xl overflow-hidden mb-6 shadow-md h-24 bg-gray-100\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-100\"></div>\n          <div className=\"relative h-full flex items-center px-6\">\n            <div className=\"max-w-4xl\">\n              <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-800\">\n                {scholarship.title}\n              </h1>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-5\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-5\">\n            {/* Key Information Section - Simple List */}\n            <section className=\"bg-white rounded-xl p-4 shadow-sm border border-gray-100\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                <FileTextOutlined className=\"text-primary mr-2\" />\n                Informations clés\n              </h2>\n              <ul className=\"text-sm space-y-1.5\">\n                {scholarship.deadline && (\n                  <li className=\"flex items-start\">\n                    <CalendarOutlined className=\"text-primary mr-2 mt-0.5\" />\n                    <div>\n                      <div className=\"flex items-center\">\n                        <span className=\"font-medium text-gray-700\">Date limite :</span>\n                        <span className=\"text-gray-600 ml-1\">\n                          {scholarship.formattedDeadline || dateUtils.formatDate(scholarship.deadline, DateFormat.MEDIUM)}\n                        </span>\n\n                        {/* Status badge */}\n                        {scholarship.isExpired ? (\n                          <Badge\n                            className=\"ml-2\"\n                            count=\"Expirée\"\n                            style={{ backgroundColor: '#ff4d4f' }}\n                          />\n                        ) : (\n                          <Badge\n                            className=\"ml-2\"\n                            count={`${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} restant${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''}`}\n                            style={{ backgroundColor: (scholarship.daysRemaining || 0) <= 7 ? '#faad14' : '#52c41a' }}\n                          />\n                        )}\n                      </div>\n\n                      {/* Days remaining indicator */}\n                      {!scholarship.isExpired && (\n                        <div className=\"text-xs text-gray-500 mt-1 flex items-center\">\n                          <ClockCircleOutlined className=\"mr-1\" />\n                          {(scholarship.daysRemaining || 0) === 0\n                            ? \"Dernier jour pour postuler !\"\n                            : `Il reste ${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} pour postuler`}\n                        </div>\n                      )}\n                    </div>\n                  </li>\n                )}\n\n                {scholarship.level && (\n                  <li className=\"flex items-center\">\n                    <UserOutlined className=\"text-primary mr-2\" />\n                    <span className=\"font-medium text-gray-700\">Niveau d'études :</span>\n                    <span className=\"text-gray-600 ml-1\">{scholarship.level}</span>\n                  </li>\n                )}\n\n                {scholarship.country && (\n                  <li className=\"flex items-center\">\n                    <GlobalOutlined className=\"text-primary mr-2\" />\n                    <span className=\"font-medium text-gray-700\">Pays :</span>\n                    <span className=\"text-gray-600 ml-1\">{scholarship.country}</span>\n                  </li>\n                )}\n\n                {scholarship.coverage && (\n                  <li className=\"flex items-center\">\n                    <TrophyOutlined className=\"text-primary mr-2\" />\n                    <span className=\"font-medium text-gray-700\">Couverture :</span>\n                    <span className=\"text-gray-600 ml-1\">{scholarship.coverage}</span>\n                  </li>\n                )}\n              </ul>\n            </section>\n\n            {/* Description Section */}\n            <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-3\">Description</h2>\n              <div className=\"prose max-w-none text-gray-600 text-sm\">\n                <p>{scholarship.description}</p>\n              </div>\n            </section>\n\n            {/* Eligibility Criteria Section */}\n            {scholarship.eligibility_summary && (\n              <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n                <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                  <CheckCircleOutlined className=\"text-primary mr-2\" />\n                  Critères d'Éligibilité\n                </h2>\n                <div className=\"text-gray-600 text-sm\">\n                  <p className=\"mb-2\">{scholarship.eligibility_summary}</p>\n                  {scholarship.eligibility_criteria_list && scholarship.eligibility_criteria_list.length > 0 && (\n                    <ul className=\"list-disc pl-5 space-y-1\">\n                      {scholarship.eligibility_criteria_list.map((criterion, index) => (\n                        <li key={index} className=\"text-gray-600\">{criterion}</li>\n                      ))}\n                    </ul>\n                  )}\n                </div>\n              </section>\n            )}\n\n            {/* Financial Benefits Section */}\n            {scholarship.financial_benefits_summary && (\n              <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n                <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                  <DollarOutlined className=\"text-primary mr-2\" />\n                  Avantages Financiers\n                </h2>\n                <div className=\"text-gray-600 text-sm\">\n                  <p className=\"mb-2\">{scholarship.financial_benefits_summary}</p>\n                  {scholarship.financial_benefits_list && scholarship.financial_benefits_list.length > 0 && (\n                    <ul className=\"list-disc pl-5 space-y-1\">\n                      {scholarship.financial_benefits_list.map((benefit, index) => (\n                        <li key={index} className=\"text-gray-600\">{benefit}</li>\n                      ))}\n                    </ul>\n                  )}\n                </div>\n              </section>\n            )}\n\n            {/* Study Fields Section */}\n            {scholarship.study_fields && scholarship.study_fields.length > 0 && (\n              <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n                <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                  <BookOutlined className=\"text-primary mr-2\" />\n                  Domaines d'Études\n                </h2>\n                <div className=\"flex flex-wrap gap-2\">\n                  {scholarship.study_fields.map((field, index) => (\n                    <Tag key={index} color=\"blue\" className=\"px-2 py-0.5 text-xs rounded-full\">\n                      {field}\n                    </Tag>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Universities Section */}\n            {scholarship.universities && scholarship.universities.length > 0 && (\n              <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n                <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                  <BankOutlined className=\"text-primary mr-2\" />\n                  Universités Participantes\n                </h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                  {scholarship.universities.map((university, index) => (\n                    <div key={index} className=\"flex items-center p-2 bg-gray-50 rounded-lg text-sm\">\n                      <BankOutlined className=\"text-gray-400 mr-2\" />\n                      <span className=\"text-gray-700\">{university}</span>\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Required Documents Section */}\n            {scholarship.required_documents && scholarship.required_documents.length > 0 && (\n              <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n                <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                  <FileTextOutlined className=\"text-primary mr-2\" />\n                  Documents Requis\n                </h2>\n                <div className=\"space-y-3\">\n                  {scholarship.required_documents.map((doc, index) => (\n                    <div key={index} className=\"bg-gray-50 p-3 rounded-lg\">\n                      <h3 className=\"font-medium text-gray-900 mb-1 text-sm\">{doc.name}</h3>\n                      <p className=\"text-gray-600 text-xs\">{doc.description}</p>\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Essential Links Section */}\n            <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                <LinkOutlined className=\"text-primary mr-2\" />\n                Liens Essentiels\n              </h2>\n              <ul className=\"space-y-2 text-sm\">\n                <li className=\"text-gray-700\">\n                  Site officiel de la bourse: {\" \"}\n                  <a\n                    href={scholarship.scholarship_link || \"https://example.com/scholarship\"}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-primary hover:text-primary-dark underline\"\n                  >\n                    {scholarship.scholarship_link || \"https://example.com/scholarship\"}\n                  </a>\n                </li>\n                <li className=\"text-gray-700\">\n                  Portail de candidature en ligne: {\" \"}\n                  <a\n                    href=\"https://example.com/apply\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-primary hover:text-primary-dark underline\"\n                  >\n                    https://example.com/apply\n                  </a>\n                </li>\n                <li className=\"text-gray-700\">\n                  Guide d'application PDF: {\" \"}\n                  <a\n                    href=\"https://example.com/guide.pdf\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-primary hover:text-primary-dark underline\"\n                  >\n                    https://example.com/guide.pdf\n                  </a>\n                </li>\n                <li className=\"text-gray-700\">\n                  FAQ sur la bourse: {\" \"}\n                  <a\n                    href=\"https://example.com/faq\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-primary hover:text-primary-dark underline\"\n                  >\n                    https://example.com/faq\n                  </a>\n                </li>\n              </ul>\n            </section>\n\n            {/* YouTube Channel Section */}\n            <section className=\"bg-white rounded-xl p-5 shadow-sm border border-gray-100\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-3 flex items-center\">\n                <YoutubeOutlined className=\"text-red-600 mr-2\" />\n                Notre Chaîne YouTube\n              </h2>\n              <ul className=\"space-y-2 text-sm\">\n                <li className=\"text-gray-700\">\n                  Tutoriel vidéo sur cette bourse: {\" \"}\n                  <a\n                    href={scholarship.youtube_link || \"https://www.youtube.com/watch?v=example1\"}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-primary hover:text-primary-dark underline\"\n                  >\n                    {scholarship.youtube_link || \"https://www.youtube.com/watch?v=example1\"}\n                  </a>\n                </li>\n                <li className=\"text-gray-700\">\n                  Comment préparer votre dossier de candidature: {\" \"}\n                  <a\n                    href=\"https://www.youtube.com/watch?v=example2\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-primary hover:text-primary-dark underline\"\n                  >\n                    https://www.youtube.com/watch?v=example2\n                  </a>\n                </li>\n                <li className=\"text-gray-700\">\n                  Visitez notre chaîne YouTube pour plus de tutoriels: {\" \"}\n                  <a\n                    href=\"https://www.youtube.com/channel/UCxxxxxxxxxxx\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-primary hover:text-primary-dark underline\"\n                  >\n                    https://www.youtube.com/channel/UCxxxxxxxxxxx\n                  </a>\n                </li>\n              </ul>\n            </section>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-4\">\n            {/* Newsletter Subscription */}\n            <div className=\"bg-white rounded-xl p-4 shadow-sm border border-gray-100\">\n              <h3 className=\"text-base font-bold text-gray-900 mb-2\">Restez informé</h3>\n              <p className=\"text-xs text-gray-600 mb-3\">\n                Recevez les dernières bourses et opportunités directement dans votre boîte mail.\n              </p>\n              <form className=\"space-y-2\">\n                <div>\n                  <input\n                    type=\"email\"\n                    placeholder=\"Votre adresse email\"\n                    className=\"w-full px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                    required\n                  />\n                </div>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  className=\"w-full\"\n                >\n                  S'abonner\n                </Button>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  En vous inscrivant, vous acceptez notre politique de confidentialité.\n                </p>\n              </form>\n            </div>\n\n            {/* Suggested Scholarships */}\n            <div className=\"bg-white rounded-xl p-4 shadow-sm border border-gray-100\">\n              <h3 className=\"text-base font-bold text-gray-900 mb-3\">Bourses Similaires</h3>\n              <div className=\"space-y-3\">\n                {/* These would be dynamically loaded in a real implementation */}\n                {[6, 7, 8, 10, 11, 13].map((scholarshipId, index) => (\n                  <div key={index} className=\"group flex items-start p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\" onClick={() => navigate(`/scholarships/${scholarshipId}`)}>\n                    <div className=\"flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden mr-2\">\n                      <img\n                        src={`/assets/scholarship${(index % 3) + 1}.jpg`}\n                        alt=\"Scholarship thumbnail\"\n                        className=\"w-full h-full object-cover group-hover:scale-105 transition-transform\"\n                        onError={(e) => {\n                          const target = e.target as HTMLImageElement;\n                          target.src = '/assets/default-thumbnail.jpg';\n                        }}\n                      />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"text-xs font-medium text-gray-900 group-hover:text-primary transition-colors line-clamp-2\">\n                        {index === 0 ? \"Fulbright Scholarship Program\" :\n                         index === 1 ? \"Chevening Scholarship\" :\n                         index === 2 ? \"DAAD Scholarship\" :\n                         index === 3 ? \"Bourse du gouvernment indien\" :\n                         index === 4 ? \"Updated Test Scholarship\" :\n                         \"Example Scholarship 1\"}\n                      </h4>\n                      <div className=\"flex items-center mt-0.5\">\n                        <CalendarOutlined className=\"text-gray-400 text-xs mr-1\" />\n                        <span className=\"text-xs text-gray-500 truncate\">\n                          {new Date(new Date().setMonth(new Date().getMonth() + index + 1)).toLocaleDateString()}\n                        </span>\n                        <GlobalOutlined className=\"text-gray-400 text-xs ml-2 mr-1\" />\n                        <span className=\"text-xs text-gray-500 truncate\">\n                          {index === 0 ? \"United States\" :\n                           index === 1 ? \"United Kingdom\" :\n                           index === 2 ? \"Germany\" :\n                           index === 3 ? \"Inde\" :\n                           index === 4 ? \"France\" :\n                           \"Canada\"}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n                <div className=\"pt-1 flex space-x-2\">\n                  <Button\n                    type=\"default\"\n                    size=\"small\"\n                    className=\"flex-1\"\n                    onClick={() => navigate('/scholarships')}\n                  >\n                    Voir plus\n                  </Button>\n                  <Button\n                    type=\"primary\"\n                    size=\"small\"\n                    className=\"flex-1\"\n                    onClick={() => navigate('/scholarships')}\n                  >\n                    Retour\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default EnhancedScholarshipDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAQC,KAAK,QAAQ,MAAM;AACtD,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,OAAOC,SAAS,IAAIC,UAAU,QAAQ,oBAAoB;AAE1D,SACEC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,cAAc,EACdC,gBAAgB,EAChBC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,eAAe,EACfC,mBAAmB,QACd,mBAAmB;;AAI1B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAgCA;AACA,MAAMC,eAAyB,GAAGA,CAAA,kBAChCH,OAAA;EAAKI,SAAS,EAAC,4DAA4D;EAAAC,QAAA,eACzEL,OAAA;IAAKI,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDL,OAAA;MAAKI,SAAS,EAAC;IAA6B;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnDT,OAAA;MAAKI,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCL,OAAA;QAAKI,SAAS,EAAC;MAA+B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrDT,OAAA;QAAKI,SAAS,EAAC;MAAgC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtDT,OAAA;QAAKI,SAAS,EAAC;MAAgC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtDT,OAAA;QAAKI,SAAS,EAAC;MAA+B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAErDT,OAAA;QAAKI,SAAS,EAAC;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1DT,OAAA;QAAKI,SAAS,EAAC;MAAgC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtDT,OAAA;QAAKI,SAAS,EAAC;MAAgC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GAhBIP,eAAyB;AAkB/B,MAAMQ,6BAAuC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpD,MAAM;IAAEC,EAAE;IAAEC;EAAK,CAAC,GAAGtC,SAAS,CAAiC,CAAC;EAChE,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAM+C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnCH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,IAAIE,aAA4B,GAAG,IAAI;;QAEvC;QACA,IAAIV,EAAE,EAAE;UACNU,aAAa,GAAGV,EAAE;QACpB;QACA;QAAA,KACK,IAAIC,IAAI,EAAE;UACb,MAAMU,WAAW,GAAGxC,iBAAiB,CAAC8B,IAAI,CAAC;UAC3C,IAAIU,WAAW,EAAE;YACfD,aAAa,GAAGC,WAAW,CAACC,QAAQ,CAAC,CAAC;UACxC,CAAC,MAAM;YACL,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;UACxC;QACF;QAEA,IAAIH,aAAa,EAAE;UACjB;UACAI,OAAO,CAACC,GAAG,CAAC,gCAAgCL,aAAa,EAAE,CAAC;UAE5D,IAAI;YACF;YACAI,OAAO,CAACC,GAAG,CAAC,wCAAwCL,aAAa,EAAE,CAAC;;YAEpE;YACA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0CP,aAAa,EAAE,CAAC;YAEvF,IAAI,CAACM,QAAQ,CAACE,EAAE,EAAE;cAChB,MAAM,IAAIL,KAAK,CAAC,uBAAuBG,QAAQ,CAACG,MAAM,EAAE,CAAC;YAC3D;YAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;YAClCP,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,IAAI,CAAC;;YAElC;YACA,MAAME,eAAe,GAAGF,IAAI,CAACA,IAAI,IAAIA,IAAI;;YAEzC;YACA,IAAIE,eAAe,EAAE;cACnBC,sBAAsB,CAACD,eAAe,CAAC;YACzC,CAAC,MAAM;cACLR,OAAO,CAACP,KAAK,CAAC,iCAAiC,EAAEa,IAAI,CAAC;cACtD,MAAM,IAAIP,KAAK,CAAC,6BAA6B,CAAC;YAChD;UACF,CAAC,CAAC,OAAOW,QAAQ,EAAE;YACjB;YACA,MAAMA,QAAQ;UAChB;QACF,CAAC,MAAM;UACL,MAAM,IAAIX,KAAK,CAAC,8BAA8B,CAAC;QACjD;MACF,CAAC,CAAC,OAAOY,GAAQ,EAAE;QACjBX,OAAO,CAACP,KAAK,CAAC,6BAA6B,EAAEkB,GAAG,CAAC;;QAEjD;QACA,IAAIA,GAAG,CAACT,QAAQ,EAAE;UAAA,IAAAU,kBAAA;UAChB;UACA;UACA,MAAMC,UAAU,GAAGF,GAAG,CAACT,QAAQ,CAACG,MAAM;UACtC,MAAMS,YAAY,GAAG,EAAAF,kBAAA,GAAAD,GAAG,CAACT,QAAQ,CAACI,IAAI,cAAAM,kBAAA,uBAAjBA,kBAAA,CAAmBG,OAAO,KAAI,mBAAmB;UAEtE,IAAIF,UAAU,KAAK,GAAG,EAAE;YACtBnB,QAAQ,CAAC,uBAAuBR,EAAE,IAAIC,IAAI,qDAAqD,CAAC;UAClG,CAAC,MAAM;YACLO,QAAQ,CAAC,UAAUmB,UAAU,KAAKC,YAAY,EAAE,CAAC;UACnD;QACF,CAAC,MAAM,IAAIH,GAAG,CAACK,OAAO,EAAE;UACtB;UACAtB,QAAQ,CAAC,yHAAyH,CAAC;UACnIM,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,GAAG,CAACK,OAAO,CAAC;QAClD,CAAC,MAAM;UACL;UACAtB,QAAQ,CAAC,iDAAiDiB,GAAG,CAACI,OAAO,EAAE,CAAC;QAC1E;MACF,CAAC,SAAS;QACRvB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACT,EAAE,EAAEC,IAAI,CAAC,CAAC;;EAEd;EACA,MAAMsB,sBAAsB,GAAIH,IAAiB,IAAK;IACpD;IACA,MAAMW,QAAQ,GAAG,OAAOX,IAAI,CAACW,QAAQ,KAAK,QAAQ,GAC9C,IAAIC,IAAI,CAACZ,IAAI,CAACW,QAAQ,CAAC,CAACE,WAAW,CAAC,CAAC,GACrCb,IAAI,CAACW,QAAQ;IAEjB,MAAMG,SAAS,GAAGd,IAAI,CAACc,SAAS,IAAI,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5D,MAAME,SAAS,GAAGf,IAAI,CAACe,SAAS,IAAI,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;;IAE5D;IACA,MAAMG,SAAS,GAAGhB,IAAI,CAACgB,SAAS,KAAKC,SAAS,GAC1CjB,IAAI,CAACgB,SAAS,GACdhE,SAAS,CAACkE,UAAU,CAACP,QAAQ,CAAC;IAElC,MAAMQ,aAAa,GAAGnB,IAAI,CAACmB,aAAa,KAAKF,SAAS,GAClDjB,IAAI,CAACmB,aAAa,GAClBnE,SAAS,CAACoE,gBAAgB,CAACT,QAAQ,CAAC;IAExC,MAAMU,iBAAiB,GAAGrB,IAAI,CAACqB,iBAAiB,IAAIrE,SAAS,CAACsE,UAAU,CAACX,QAAQ,EAAE1D,UAAU,CAACsE,MAAM,CAAC;;IAErG;IACA,MAAMC,YAAY,GAAG;MACnB,GAAGxB,IAAI;MACPW,QAAQ;MACRG,SAAS;MACTC,SAAS;MACTC,SAAS;MACTG,aAAa;MACbE,iBAAiB;MAEjB;MACAI,uBAAuB,EAAEzB,IAAI,CAAC0B,0BAA0B,GACpD1B,IAAI,CAAC0B,0BAA0B,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GACnE,EAAE;MAEN;MACAC,yBAAyB,EAAE/B,IAAI,CAACgC,mBAAmB,GAC/ChC,IAAI,CAACgC,mBAAmB,CAACL,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAC5D,EAAE;MAEN;MACA;MACAG,YAAY,EAAEjC,IAAI,CAACiC,YAAY,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAAE,UAAU,CAAC;MAC9FC,YAAY,EAAElC,IAAI,CAACkC,YAAY,IAAI,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,CAAC;MACxGC,kBAAkB,EAAEnC,IAAI,CAACmC,kBAAkB,IAAI,CAC7C;QAAEC,IAAI,EAAE,WAAW;QAAEC,WAAW,EAAE;MAA2B,CAAC,EAC9D;QAAED,IAAI,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAA8C,CAAC,EACzF;QAAED,IAAI,EAAE,sBAAsB;QAAEC,WAAW,EAAE;MAA6C,CAAC,CAC5F;MACDC,oBAAoB,EAAEtC,IAAI,CAACsC,oBAAoB,IAAI;IACrD,CAAC;;IAED;IACA5C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE6B,YAAY,CAAC;IAExDxC,cAAc,CAACwC,YAAY,CAAC;EAC9B,CAAC;;EAED;EACA,IAAIvC,OAAO,EAAE;IACX,oBAAOlB,OAAA,CAACG,eAAe;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5B;;EAEA;EACA,IAAIW,KAAK,IAAI,CAACJ,WAAW,EAAE;IACzB,oBACEhB,OAAA;MAAKI,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DL,OAAA,CAACpB,KAAK;QACJ8D,OAAO,EAAC,uBAAoB;QAC5B4B,WAAW,EAAElD,KAAK,IAAI,gEAAiE;QACvFoD,IAAI,EAAC,OAAO;QACZC,QAAQ;QACRrE,SAAS,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACFT,OAAA;QAAKI,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBL,OAAA,CAACtB,IAAI;UACHgG,EAAE,EAAC,eAAe;UAClBtE,SAAS,EAAC,0NAA0N;UAAAC,QAAA,EACrO;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACET,OAAA,CAAAE,SAAA;IAAAG,QAAA,gBAEEL,OAAA,CAACrB,MAAM;MAAA0B,QAAA,gBACLL,OAAA;QAAAK,QAAA,GAAQW,WAAW,CAAC2D,KAAK,EAAC,aAAW;MAAA;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7CT,OAAA;QAAMqE,IAAI,EAAC,aAAa;QAACO,OAAO,EAAE,GAAG5D,WAAW,CAAC2D,KAAK,MAAM3D,WAAW,CAACsD,WAAW,CAACO,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;MAAM;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9GT,OAAA;QAAM8E,QAAQ,EAAC,UAAU;QAACF,OAAO,EAAE,GAAG5D,WAAW,CAAC2D,KAAK;MAAc;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxET,OAAA;QAAM8E,QAAQ,EAAC,gBAAgB;QAACF,OAAO,EAAE,GAAG5D,WAAW,CAAC2D,KAAK,MAAM3D,WAAW,CAACsD,WAAW,CAACO,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;MAAM;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACpHO,WAAW,CAAC+D,SAAS,iBAAI/E,OAAA;QAAM8E,QAAQ,EAAC,UAAU;QAACF,OAAO,EAAE5D,WAAW,CAAC+D;MAAU;QAAAzE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtFT,OAAA;QAAQwE,IAAI,EAAC,qBAAqB;QAAAnE,QAAA,EAC/B2E,IAAI,CAACC,SAAS,CAAC;UACd,UAAU,EAAE,oBAAoB;UAChC,OAAO,EAAE,mCAAmC;UAC5C,MAAM,EAAEjE,WAAW,CAAC2D,KAAK;UACzB,aAAa,EAAE3D,WAAW,CAACsD,WAAW;UACtC,kBAAkB,EAAEtD,WAAW,CAACkE,KAAK;UACrC,SAAS,EAAE;YACT,OAAO,EAAE,SAAS;YAClB,MAAM,EAAElE,WAAW,CAACmE;UACtB,CAAC;UACD,YAAY,EAAEnE,WAAW,CAAC4B;QAC5B,CAAC;MAAC;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAETT,OAAA;MAAKI,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DL,OAAA;QAAKI,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClFL,OAAA;UAAKI,SAAS,EAAC;QAA6D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnFT,OAAA;UAAKI,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDL,OAAA;YAAKI,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBL,OAAA;cAAII,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EACzDW,WAAW,CAAC2D;YAAK;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENT,OAAA;QAAKI,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDL,OAAA;UAAKI,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtCL,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACP,gBAAgB;gBAACW,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAII,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAChCW,WAAW,CAAC4B,QAAQ,iBACnB5C,OAAA;gBAAII,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BL,OAAA,CAACb,gBAAgB;kBAACiB,SAAS,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzDT,OAAA;kBAAAK,QAAA,gBACEL,OAAA;oBAAKI,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCL,OAAA;sBAAMI,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChET,OAAA;sBAAMI,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EACjCW,WAAW,CAACsC,iBAAiB,IAAIrE,SAAS,CAACsE,UAAU,CAACvC,WAAW,CAAC4B,QAAQ,EAAE1D,UAAU,CAACsE,MAAM;oBAAC;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,EAGNO,WAAW,CAACiC,SAAS,gBACpBjD,OAAA,CAACjB,KAAK;sBACJqB,SAAS,EAAC,MAAM;sBAChBgF,KAAK,EAAC,YAAS;sBACfC,KAAK,EAAE;wBAAEC,eAAe,EAAE;sBAAU;oBAAE;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,gBAEFT,OAAA,CAACjB,KAAK;sBACJqB,SAAS,EAAC,MAAM;sBAChBgF,KAAK,EAAE,GAAGpE,WAAW,CAACoC,aAAa,IAAI,CAAC,QAAQ,CAACpC,WAAW,CAACoC,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,WAAW,CAACpC,WAAW,CAACoC,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,EAAG;sBAChKiC,KAAK,EAAE;wBAAEC,eAAe,EAAE,CAACtE,WAAW,CAACoC,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG;sBAAU;oBAAE;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAGL,CAACO,WAAW,CAACiC,SAAS,iBACrBjD,OAAA;oBAAKI,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,gBAC3DL,OAAA,CAACF,mBAAmB;sBAACM,SAAS,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvC,CAACO,WAAW,CAACoC,aAAa,IAAI,CAAC,MAAM,CAAC,GACnC,8BAA8B,GAC9B,YAAYpC,WAAW,CAACoC,aAAa,IAAI,CAAC,QAAQ,CAACpC,WAAW,CAACoC,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,gBAAgB;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACL,EAEAO,WAAW,CAACkE,KAAK,iBAChBlF,OAAA;gBAAII,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BL,OAAA,CAACJ,YAAY;kBAACQ,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CT,OAAA;kBAAMI,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpET,OAAA;kBAAMI,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEW,WAAW,CAACkE;gBAAK;kBAAA5E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACL,EAEAO,WAAW,CAACmE,OAAO,iBAClBnF,OAAA;gBAAII,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BL,OAAA,CAACZ,cAAc;kBAACgB,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDT,OAAA;kBAAMI,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzDT,OAAA;kBAAMI,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEW,WAAW,CAACmE;gBAAO;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CACL,EAEAO,WAAW,CAACuE,QAAQ,iBACnBvF,OAAA;gBAAII,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BL,OAAA,CAACL,cAAc;kBAACS,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDT,OAAA;kBAAMI,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DT,OAAA;kBAAMI,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEW,WAAW,CAACuE;gBAAQ;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGVT,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrET,OAAA;cAAKI,SAAS,EAAC,wCAAwC;cAAAC,QAAA,eACrDL,OAAA;gBAAAK,QAAA,EAAIW,WAAW,CAACsD;cAAW;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGTO,WAAW,CAACiD,mBAAmB,iBAC9BjE,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACT,mBAAmB;gBAACa,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mCAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAKI,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCL,OAAA;gBAAGI,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEW,WAAW,CAACiD;cAAmB;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACxDO,WAAW,CAACgD,yBAAyB,IAAIhD,WAAW,CAACgD,yBAAyB,CAACwB,MAAM,GAAG,CAAC,iBACxFxF,OAAA;gBAAII,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrCW,WAAW,CAACgD,yBAAyB,CAACH,GAAG,CAAC,CAAC4B,SAAS,EAAEC,KAAK,kBAC1D1F,OAAA;kBAAgBI,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEoF;gBAAS,GAA3CC,KAAK;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2C,CAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV,EAGAO,WAAW,CAAC2C,0BAA0B,iBACrC3D,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACR,cAAc;gBAACY,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAElD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAKI,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCL,OAAA;gBAAGI,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEW,WAAW,CAAC2C;cAA0B;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC/DO,WAAW,CAAC0C,uBAAuB,IAAI1C,WAAW,CAAC0C,uBAAuB,CAAC8B,MAAM,GAAG,CAAC,iBACpFxF,OAAA;gBAAII,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrCW,WAAW,CAAC0C,uBAAuB,CAACG,GAAG,CAAC,CAAC8B,OAAO,EAAED,KAAK,kBACtD1F,OAAA;kBAAgBI,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEsF;gBAAO,GAAzCD,KAAK;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAyC,CACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV,EAGAO,WAAW,CAACkD,YAAY,IAAIlD,WAAW,CAACkD,YAAY,CAACsB,MAAM,GAAG,CAAC,iBAC9DxF,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACV,YAAY;gBAACc,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAKI,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCW,WAAW,CAACkD,YAAY,CAACL,GAAG,CAAC,CAAC+B,KAAK,EAAEF,KAAK,kBACzC1F,OAAA,CAACnB,GAAG;gBAAagH,KAAK,EAAC,MAAM;gBAACzF,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EACvEuF;cAAK,GADEF,KAAK;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV,EAGAO,WAAW,CAACmD,YAAY,IAAInD,WAAW,CAACmD,YAAY,CAACqB,MAAM,GAAG,CAAC,iBAC9DxF,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACX,YAAY;gBAACe,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAKI,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDW,WAAW,CAACmD,YAAY,CAACN,GAAG,CAAC,CAACiC,UAAU,EAAEJ,KAAK,kBAC9C1F,OAAA;gBAAiBI,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,gBAC9EL,OAAA,CAACX,YAAY;kBAACe,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CT,OAAA;kBAAMI,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEyF;gBAAU;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAF3CiF,KAAK;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV,EAGAO,WAAW,CAACoD,kBAAkB,IAAIpD,WAAW,CAACoD,kBAAkB,CAACoB,MAAM,GAAG,CAAC,iBAC1ExF,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACP,gBAAgB;gBAACW,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAKI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBW,WAAW,CAACoD,kBAAkB,CAACP,GAAG,CAAC,CAACkC,GAAG,EAAEL,KAAK,kBAC7C1F,OAAA;gBAAiBI,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACpDL,OAAA;kBAAII,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAE0F,GAAG,CAAC1B;gBAAI;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtET,OAAA;kBAAGI,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE0F,GAAG,CAACzB;gBAAW;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA,GAFlDiF,KAAK;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV,eAGDT,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACN,YAAY;gBAACU,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAII,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BL,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,8BACA,EAAC,GAAG,eAChCL,OAAA;kBACEgG,IAAI,EAAEhF,WAAW,CAACiF,gBAAgB,IAAI,iCAAkC;kBACxEC,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB/F,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAEzDW,WAAW,CAACiF,gBAAgB,IAAI;gBAAiC;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACLT,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,mCACK,EAAC,GAAG,eACrCL,OAAA;kBACEgG,IAAI,EAAC,2BAA2B;kBAChCE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB/F,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAC3D;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACLT,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,2BACH,EAAC,GAAG,eAC7BL,OAAA;kBACEgG,IAAI,EAAC,+BAA+B;kBACpCE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB/F,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAC3D;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACLT,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,qBACT,EAAC,GAAG,eACvBL,OAAA;kBACEgG,IAAI,EAAC,yBAAyB;kBAC9BE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB/F,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAC3D;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGVT,OAAA;YAASI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAC3EL,OAAA;cAAII,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACpEL,OAAA,CAACH,eAAe;gBAACO,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,2BAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLT,OAAA;cAAII,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BL,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,sCACK,EAAC,GAAG,eACrCL,OAAA;kBACEgG,IAAI,EAAEhF,WAAW,CAACoF,YAAY,IAAI,0CAA2C;kBAC7EF,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB/F,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAEzDW,WAAW,CAACoF,YAAY,IAAI;gBAA0C;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACLT,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,oDACmB,EAAC,GAAG,eACnDL,OAAA;kBACEgG,IAAI,EAAC,0CAA0C;kBAC/CE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB/F,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAC3D;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACLT,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,0DACyB,EAAC,GAAG,eACzDL,OAAA;kBACEgG,IAAI,EAAC,+CAA+C;kBACpDE,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB/F,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAC3D;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNT,OAAA;UAAKI,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBL,OAAA;YAAKI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACvEL,OAAA;cAAII,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ET,OAAA;cAAGI,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJT,OAAA;cAAMI,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACzBL,OAAA;gBAAAK,QAAA,eACEL,OAAA;kBACEwE,IAAI,EAAC,OAAO;kBACZ6B,WAAW,EAAC,qBAAqB;kBACjCjG,SAAS,EAAC,0IAA0I;kBACpJkG,QAAQ;gBAAA;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNT,OAAA,CAAClB,MAAM;gBACL0F,IAAI,EAAC,SAAS;gBACd+B,IAAI,EAAC,OAAO;gBACZnG,SAAS,EAAC,QAAQ;gBAAAC,QAAA,EACnB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTT,OAAA;gBAAGI,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNT,OAAA;YAAKI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACvEL,OAAA;cAAII,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ET,OAAA;cAAKI,SAAS,EAAC,WAAW;cAAAC,QAAA,GAEvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACwD,GAAG,CAAC,CAACtC,aAAa,EAAEmE,KAAK,kBAC9C1F,OAAA;gBAAiBI,SAAS,EAAC,yFAAyF;gBAACoG,OAAO,EAAEA,CAAA,KAAMzF,QAAQ,CAAC,iBAAiBQ,aAAa,EAAE,CAAE;gBAAAlB,QAAA,gBAC7KL,OAAA;kBAAKI,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,eACtEL,OAAA;oBACEyG,GAAG,EAAE,sBAAuBf,KAAK,GAAG,CAAC,GAAI,CAAC,MAAO;oBACjDgB,GAAG,EAAC,uBAAuB;oBAC3BtG,SAAS,EAAC,uEAAuE;oBACjFuG,OAAO,EAAGC,CAAC,IAAK;sBACd,MAAMV,MAAM,GAAGU,CAAC,CAACV,MAA0B;sBAC3CA,MAAM,CAACO,GAAG,GAAG,+BAA+B;oBAC9C;kBAAE;oBAAAnG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNT,OAAA;kBAAKI,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BL,OAAA;oBAAII,SAAS,EAAC,2FAA2F;oBAAAC,QAAA,EACtGqF,KAAK,KAAK,CAAC,GAAG,+BAA+B,GAC7CA,KAAK,KAAK,CAAC,GAAG,uBAAuB,GACrCA,KAAK,KAAK,CAAC,GAAG,kBAAkB,GAChCA,KAAK,KAAK,CAAC,GAAG,8BAA8B,GAC5CA,KAAK,KAAK,CAAC,GAAG,0BAA0B,GACxC;kBAAuB;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACLT,OAAA;oBAAKI,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACvCL,OAAA,CAACb,gBAAgB;sBAACiB,SAAS,EAAC;oBAA4B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3DT,OAAA;sBAAMI,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAC7C,IAAIwC,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACgE,QAAQ,CAAC,IAAIhE,IAAI,CAAC,CAAC,CAACiE,QAAQ,CAAC,CAAC,GAAGpB,KAAK,GAAG,CAAC,CAAC,CAAC,CAACqB,kBAAkB,CAAC;oBAAC;sBAAAzG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC,eACPT,OAAA,CAACZ,cAAc;sBAACgB,SAAS,EAAC;oBAAiC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9DT,OAAA;sBAAMI,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAC7CqF,KAAK,KAAK,CAAC,GAAG,eAAe,GAC7BA,KAAK,KAAK,CAAC,GAAG,gBAAgB,GAC9BA,KAAK,KAAK,CAAC,GAAG,SAAS,GACvBA,KAAK,KAAK,CAAC,GAAG,MAAM,GACpBA,KAAK,KAAK,CAAC,GAAG,QAAQ,GACtB;oBAAQ;sBAAApF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GApCEiF,KAAK;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCV,CACN,CAAC,eACFT,OAAA;gBAAKI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCL,OAAA,CAAClB,MAAM;kBACL0F,IAAI,EAAC,SAAS;kBACd+B,IAAI,EAAC,OAAO;kBACZnG,SAAS,EAAC,QAAQ;kBAClBoG,OAAO,EAAEA,CAAA,KAAMzF,QAAQ,CAAC,eAAe,CAAE;kBAAAV,QAAA,EAC1C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTT,OAAA,CAAClB,MAAM;kBACL0F,IAAI,EAAC,SAAS;kBACd+B,IAAI,EAAC,OAAO;kBACZnG,SAAS,EAAC,QAAQ;kBAClBoG,OAAO,EAAEA,CAAA,KAAMzF,QAAQ,CAAC,eAAe,CAAE;kBAAAV,QAAA,EAC1C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACG,EAAA,CAllBID,6BAAuC;EAAA,QACtBnC,SAAS,EACbC,WAAW;AAAA;AAAAuI,GAAA,GAFxBrG,6BAAuC;AAolB7C,eAAeA,6BAA6B;AAAC,IAAAD,EAAA,EAAAsG,GAAA;AAAAC,YAAA,CAAAvG,EAAA;AAAAuG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}