{"ast": null, "code": "import axios from 'axios';\n// Base API URL\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n/**\n * Service for fetching and filtering scholarships\n */\nconst scholarshipService = {\n  /**\n   * Get all scholarships\n   */\n  getAllScholarships: async () => {\n    try {\n      console.log('Fetching scholarships from API...');\n      const response = await axios.get(`${API_URL}/api/scholarships`);\n      console.log('API response:', response.data);\n\n      // Handle the correct API response format: { success: true, data: [...] }\n      const scholarshipsData = response.data.data || response.data.scholarships || [];\n      return scholarshipsData.map(scholarship => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France'\n      }));\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n      return getFallbackScholarships();\n    }\n  },\n  /**\n   * Get scholarships by level (Licence, Master, Doctorat)\n   */\n  getScholarshipsByLevel: async level => {\n    try {\n      const response = await axios.get(`${API_URL}/api/scholarships?level=${level}`);\n      return response.data.scholarships.map(scholarship => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France'\n      }));\n    } catch (error) {\n      console.error(`Error fetching scholarships by level ${level}:`, error);\n      // Filter fallback data by level\n      return getFallbackScholarships().filter(s => {\n        var _s$level;\n        return ((_s$level = s.level) === null || _s$level === void 0 ? void 0 : _s$level.toLowerCase()) === level.toLowerCase();\n      });\n    }\n  },\n  /**\n   * Get scholarships by funding source\n   */\n  getScholarshipsBySource: async source => {\n    try {\n      const response = await axios.get(`${API_URL}/api/scholarships?fundingSource=${source}`);\n      return response.data.scholarships.map(scholarship => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France'\n      }));\n    } catch (error) {\n      console.error(`Error fetching scholarships by source ${source}:`, error);\n      // Filter fallback data by source\n      return getFallbackScholarships().filter(s => {\n        var _s$fundingSource;\n        return (_s$fundingSource = s.fundingSource) === null || _s$fundingSource === void 0 ? void 0 : _s$fundingSource.toLowerCase().includes(source.toLowerCase());\n      });\n    }\n  },\n  /**\n   * Get latest scholarships\n   */\n  getLatestScholarships: async (limit = 6) => {\n    try {\n      const response = await axios.get(`${API_URL}/api/scholarships?sortBy=createdAt&sortOrder=desc&limit=${limit}`);\n      return response.data.scholarships.map(scholarship => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France'\n      }));\n    } catch (error) {\n      console.error('Error fetching latest scholarships:', error);\n      // Return first 'limit' scholarships from fallback data\n      return getFallbackScholarships().slice(0, limit);\n    }\n  }\n};\n\n/**\n * Helper function to determine funding source from scholarship data\n */\nfunction determineSourceFromData(scholarship) {\n  var _scholarship$title, _scholarship$descript, _scholarship$title2, _scholarship$descript2;\n  if ((_scholarship$title = scholarship.title) !== null && _scholarship$title !== void 0 && _scholarship$title.toLowerCase().includes('gouvernement') || (_scholarship$descript = scholarship.description) !== null && _scholarship$descript !== void 0 && _scholarship$descript.toLowerCase().includes('gouvernement')) {\n    return 'Gouvernement';\n  } else if ((_scholarship$title2 = scholarship.title) !== null && _scholarship$title2 !== void 0 && _scholarship$title2.toLowerCase().includes('université') || (_scholarship$descript2 = scholarship.description) !== null && _scholarship$descript2 !== void 0 && _scholarship$descript2.toLowerCase().includes('université')) {\n    return 'Université';\n  } else {\n    return 'Organisation';\n  }\n}\n\n/**\n * Fallback scholarships data when API is unavailable\n */\nfunction getFallbackScholarships() {\n  return [{\n    id: 1,\n    title: 'Bourse d\\'Excellence en Informatique',\n    thumbnail: '/assets/scholarship1.jpg',\n    deadline: '2024-06-30',\n    isOpen: true,\n    level: 'Licence',\n    fundingSource: 'Université',\n    country: 'France'\n  }, {\n    id: 2,\n    title: 'Programme de Bourse en Génie Civil',\n    thumbnail: '/assets/scholarship2.jpg',\n    deadline: '2024-05-15',\n    isOpen: true,\n    level: 'Master',\n    fundingSource: 'Gouvernement',\n    country: 'Canada'\n  }, {\n    id: 3,\n    title: 'Bourse Internationale en Médecine',\n    thumbnail: '/assets/scholarship3.jpg',\n    deadline: '2024-04-30',\n    isOpen: false,\n    level: 'Doctorat',\n    fundingSource: 'Organisation',\n    country: 'Belgique'\n  }\n  // Add more fallback scholarships as needed\n  ];\n}\nexport default scholarshipService;", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "scholarshipService", "getAllScholarships", "console", "log", "response", "get", "data", "scholarshipsData", "scholarships", "map", "scholarship", "id", "title", "thumbnail", "deadline", "isOpen", "level", "fundingSource", "determineSourceFromData", "country", "error", "getFallbackScholarships", "getScholarshipsByLevel", "filter", "s", "_s$level", "toLowerCase", "getScholarshipsBySource", "source", "_s$fundingSource", "includes", "getLatestScholarships", "limit", "slice", "_scholarship$title", "_scholarship$descript", "_scholarship$title2", "_scholarship$descript2", "description"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Scholarship } from '../components/ScholarshipGrid';\n\n// Base API URL\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n/**\n * Service for fetching and filtering scholarships\n */\nconst scholarshipService = {\n  /**\n   * Get all scholarships\n   */\n  getAllScholarships: async (): Promise<Scholarship[]> => {\n    try {\n      console.log('Fetching scholarships from API...');\n      const response = await axios.get(`${API_URL}/api/scholarships`);\n      console.log('API response:', response.data);\n\n      // Handle the correct API response format: { success: true, data: [...] }\n      const scholarshipsData = response.data.data || response.data.scholarships || [];\n\n      return scholarshipsData.map((scholarship: any) => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France',\n      }));\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n      return getFallbackScholarships();\n    }\n  },\n\n  /**\n   * Get scholarships by level (Licence, Master, Doctorat)\n   */\n  getScholarshipsByLevel: async (level: string): Promise<Scholarship[]> => {\n    try {\n      const response = await axios.get(`${API_URL}/api/scholarships?level=${level}`);\n      return response.data.scholarships.map((scholarship: any) => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France',\n      }));\n    } catch (error) {\n      console.error(`Error fetching scholarships by level ${level}:`, error);\n      // Filter fallback data by level\n      return getFallbackScholarships().filter(\n        (s) => s.level?.toLowerCase() === level.toLowerCase()\n      );\n    }\n  },\n\n  /**\n   * Get scholarships by funding source\n   */\n  getScholarshipsBySource: async (source: string): Promise<Scholarship[]> => {\n    try {\n      const response = await axios.get(`${API_URL}/api/scholarships?fundingSource=${source}`);\n      return response.data.scholarships.map((scholarship: any) => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France',\n      }));\n    } catch (error) {\n      console.error(`Error fetching scholarships by source ${source}:`, error);\n      // Filter fallback data by source\n      return getFallbackScholarships().filter(\n        (s) => s.fundingSource?.toLowerCase().includes(source.toLowerCase())\n      );\n    }\n  },\n\n  /**\n   * Get latest scholarships\n   */\n  getLatestScholarships: async (limit: number = 6): Promise<Scholarship[]> => {\n    try {\n      const response = await axios.get(`${API_URL}/api/scholarships?sortBy=createdAt&sortOrder=desc&limit=${limit}`);\n      return response.data.scholarships.map((scholarship: any) => ({\n        id: scholarship.id,\n        title: scholarship.title,\n        thumbnail: scholarship.thumbnail,\n        deadline: scholarship.deadline,\n        isOpen: scholarship.isOpen,\n        level: scholarship.level,\n        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),\n        country: scholarship.country || 'France',\n      }));\n    } catch (error) {\n      console.error('Error fetching latest scholarships:', error);\n      // Return first 'limit' scholarships from fallback data\n      return getFallbackScholarships().slice(0, limit);\n    }\n  },\n};\n\n/**\n * Helper function to determine funding source from scholarship data\n */\nfunction determineSourceFromData(scholarship: any): string {\n  if (scholarship.title?.toLowerCase().includes('gouvernement') ||\n      scholarship.description?.toLowerCase().includes('gouvernement')) {\n    return 'Gouvernement';\n  } else if (scholarship.title?.toLowerCase().includes('université') ||\n             scholarship.description?.toLowerCase().includes('université')) {\n    return 'Université';\n  } else {\n    return 'Organisation';\n  }\n}\n\n/**\n * Fallback scholarships data when API is unavailable\n */\nfunction getFallbackScholarships(): Scholarship[] {\n  return [\n    {\n      id: 1,\n      title: 'Bourse d\\'Excellence en Informatique',\n      thumbnail: '/assets/scholarship1.jpg',\n      deadline: '2024-06-30',\n      isOpen: true,\n      level: 'Licence',\n      fundingSource: 'Université',\n      country: 'France',\n    },\n    {\n      id: 2,\n      title: 'Programme de Bourse en Génie Civil',\n      thumbnail: '/assets/scholarship2.jpg',\n      deadline: '2024-05-15',\n      isOpen: true,\n      level: 'Master',\n      fundingSource: 'Gouvernement',\n      country: 'Canada',\n    },\n    {\n      id: 3,\n      title: 'Bourse Internationale en Médecine',\n      thumbnail: '/assets/scholarship3.jpg',\n      deadline: '2024-04-30',\n      isOpen: false,\n      level: 'Doctorat',\n      fundingSource: 'Organisation',\n      country: 'Belgique',\n    },\n    // Add more fallback scholarships as needed\n  ];\n}\n\nexport default scholarshipService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB;AACA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAExE;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG;EACzB;AACF;AACA;EACEC,kBAAkB,EAAE,MAAAA,CAAA,KAAoC;IACtD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,MAAMC,QAAQ,GAAG,MAAMT,KAAK,CAACU,GAAG,CAAC,GAAGT,OAAO,mBAAmB,CAAC;MAC/DM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,QAAQ,CAACE,IAAI,CAAC;;MAE3C;MACA,MAAMC,gBAAgB,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACE,YAAY,IAAI,EAAE;MAE/E,OAAOD,gBAAgB,CAACE,GAAG,CAAEC,WAAgB,KAAM;QACjDC,EAAE,EAAED,WAAW,CAACC,EAAE;QAClBC,KAAK,EAAEF,WAAW,CAACE,KAAK;QACxBC,SAAS,EAAEH,WAAW,CAACG,SAAS;QAChCC,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;QAC9BC,MAAM,EAAEL,WAAW,CAACK,MAAM;QAC1BC,KAAK,EAAEN,WAAW,CAACM,KAAK;QACxBC,aAAa,EAAEP,WAAW,CAACO,aAAa,IAAIC,uBAAuB,CAACR,WAAW,CAAC;QAChFS,OAAO,EAAET,WAAW,CAACS,OAAO,IAAI;MAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAOC,uBAAuB,CAAC,CAAC;IAClC;EACF,CAAC;EAED;AACF;AACA;EACEC,sBAAsB,EAAE,MAAON,KAAa,IAA6B;IACvE,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMT,KAAK,CAACU,GAAG,CAAC,GAAGT,OAAO,2BAA2BoB,KAAK,EAAE,CAAC;MAC9E,OAAOZ,QAAQ,CAACE,IAAI,CAACE,YAAY,CAACC,GAAG,CAAEC,WAAgB,KAAM;QAC3DC,EAAE,EAAED,WAAW,CAACC,EAAE;QAClBC,KAAK,EAAEF,WAAW,CAACE,KAAK;QACxBC,SAAS,EAAEH,WAAW,CAACG,SAAS;QAChCC,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;QAC9BC,MAAM,EAAEL,WAAW,CAACK,MAAM;QAC1BC,KAAK,EAAEN,WAAW,CAACM,KAAK;QACxBC,aAAa,EAAEP,WAAW,CAACO,aAAa,IAAIC,uBAAuB,CAACR,WAAW,CAAC;QAChFS,OAAO,EAAET,WAAW,CAACS,OAAO,IAAI;MAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,wCAAwCJ,KAAK,GAAG,EAAEI,KAAK,CAAC;MACtE;MACA,OAAOC,uBAAuB,CAAC,CAAC,CAACE,MAAM,CACpCC,CAAC;QAAA,IAAAC,QAAA;QAAA,OAAK,EAAAA,QAAA,GAAAD,CAAC,CAACR,KAAK,cAAAS,QAAA,uBAAPA,QAAA,CAASC,WAAW,CAAC,CAAC,MAAKV,KAAK,CAACU,WAAW,CAAC,CAAC;MAAA,CACvD,CAAC;IACH;EACF,CAAC;EAED;AACF;AACA;EACEC,uBAAuB,EAAE,MAAOC,MAAc,IAA6B;IACzE,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMT,KAAK,CAACU,GAAG,CAAC,GAAGT,OAAO,mCAAmCgC,MAAM,EAAE,CAAC;MACvF,OAAOxB,QAAQ,CAACE,IAAI,CAACE,YAAY,CAACC,GAAG,CAAEC,WAAgB,KAAM;QAC3DC,EAAE,EAAED,WAAW,CAACC,EAAE;QAClBC,KAAK,EAAEF,WAAW,CAACE,KAAK;QACxBC,SAAS,EAAEH,WAAW,CAACG,SAAS;QAChCC,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;QAC9BC,MAAM,EAAEL,WAAW,CAACK,MAAM;QAC1BC,KAAK,EAAEN,WAAW,CAACM,KAAK;QACxBC,aAAa,EAAEP,WAAW,CAACO,aAAa,IAAIC,uBAAuB,CAACR,WAAW,CAAC;QAChFS,OAAO,EAAET,WAAW,CAACS,OAAO,IAAI;MAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,yCAAyCQ,MAAM,GAAG,EAAER,KAAK,CAAC;MACxE;MACA,OAAOC,uBAAuB,CAAC,CAAC,CAACE,MAAM,CACpCC,CAAC;QAAA,IAAAK,gBAAA;QAAA,QAAAA,gBAAA,GAAKL,CAAC,CAACP,aAAa,cAAAY,gBAAA,uBAAfA,gBAAA,CAAiBH,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACF,MAAM,CAACF,WAAW,CAAC,CAAC,CAAC;MAAA,CACtE,CAAC;IACH;EACF,CAAC;EAED;AACF;AACA;EACEK,qBAAqB,EAAE,MAAAA,CAAOC,KAAa,GAAG,CAAC,KAA6B;IAC1E,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMT,KAAK,CAACU,GAAG,CAAC,GAAGT,OAAO,2DAA2DoC,KAAK,EAAE,CAAC;MAC9G,OAAO5B,QAAQ,CAACE,IAAI,CAACE,YAAY,CAACC,GAAG,CAAEC,WAAgB,KAAM;QAC3DC,EAAE,EAAED,WAAW,CAACC,EAAE;QAClBC,KAAK,EAAEF,WAAW,CAACE,KAAK;QACxBC,SAAS,EAAEH,WAAW,CAACG,SAAS;QAChCC,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;QAC9BC,MAAM,EAAEL,WAAW,CAACK,MAAM;QAC1BC,KAAK,EAAEN,WAAW,CAACM,KAAK;QACxBC,aAAa,EAAEP,WAAW,CAACO,aAAa,IAAIC,uBAAuB,CAACR,WAAW,CAAC;QAChFS,OAAO,EAAET,WAAW,CAACS,OAAO,IAAI;MAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D;MACA,OAAOC,uBAAuB,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC,EAAED,KAAK,CAAC;IAClD;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA,SAASd,uBAAuBA,CAACR,WAAgB,EAAU;EAAA,IAAAwB,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,sBAAA;EACzD,IAAI,CAAAH,kBAAA,GAAAxB,WAAW,CAACE,KAAK,cAAAsB,kBAAA,eAAjBA,kBAAA,CAAmBR,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,cAAc,CAAC,KAAAK,qBAAA,GACzDzB,WAAW,CAAC4B,WAAW,cAAAH,qBAAA,eAAvBA,qBAAA,CAAyBT,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,cAAc,CAAC,EAAE;IACnE,OAAO,cAAc;EACvB,CAAC,MAAM,IAAI,CAAAM,mBAAA,GAAA1B,WAAW,CAACE,KAAK,cAAAwB,mBAAA,eAAjBA,mBAAA,CAAmBV,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,YAAY,CAAC,KAAAO,sBAAA,GACvD3B,WAAW,CAAC4B,WAAW,cAAAD,sBAAA,eAAvBA,sBAAA,CAAyBX,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;IACxE,OAAO,YAAY;EACrB,CAAC,MAAM;IACL,OAAO,cAAc;EACvB;AACF;;AAEA;AACA;AACA;AACA,SAAST,uBAAuBA,CAAA,EAAkB;EAChD,OAAO,CACL;IACEV,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,sCAAsC;IAC7CC,SAAS,EAAE,0BAA0B;IACrCC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,SAAS;IAChBC,aAAa,EAAE,YAAY;IAC3BE,OAAO,EAAE;EACX,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oCAAoC;IAC3CC,SAAS,EAAE,0BAA0B;IACrCC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE,cAAc;IAC7BE,OAAO,EAAE;EACX,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,mCAAmC;IAC1CC,SAAS,EAAE,0BAA0B;IACrCC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,UAAU;IACjBC,aAAa,EAAE,cAAc;IAC7BE,OAAO,EAAE;EACX;EACA;EAAA,CACD;AACH;AAEA,eAAenB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}